import { <PERSON>sia, t } from "elysia";
import {
	signupUser,
	signinUser,
	signoutUser,
	verifyEmail,
	forgotPassword,
	resetPassword,
	getUserProfile,
	updateUserProfile,
	changePassword,
	refreshNewToken,
	uploadUserProfileImage,
	uploadUserCoverImage,
	generateUserUploadSignature,
} from "./user.service";
import type { SignupUserInput, SigninUserInput } from "./user.service";
import { userAuthPlugin } from "@/core/plugins/auth";
import {
	signupSchema,
	signinSchema,
	forgotPasswordSchema,
	resetPasswordSchema,
	changePasswordSchema,
	authResponseUserSchema,
	messageResponseSchema,
	userProfileResponseSchema,
} from "./user.schema";
import { HttpError } from "@/core/utils/error";

export const userRoutes = new Elysia({ prefix: "/user" })
	.post(
		"/signup",
		async ({ body }) => {
			const { email, password } = body as SignupUserInput;
			const { user, token, refreshToken } = await signupUser({
				email,
				password,
			});
			return {
				success: true,
				message: "สมัครสมาชิกสำเร็จ",
				statusMessage: "สำเร็จ!",
				timestamp: new Date().toISOString(),
				data: { user, token, refreshToken },
			};
		},
		{
			body: signupSchema,
			response: authResponseUserSchema,
		},
	)

	.post(
		"/signin",
		async ({ body, cookie, set, headers }) => {
			const { email, password } = body as SigninUserInput;

			// ✅ ดึง IP และ User Agent สำหรับ login attempt tracking
			const ip = headers['x-forwarded-for'] ||
				headers['cf-connecting-ip'] ||
				headers['x-real-ip'] ||
				'unknown';
			const userAgent = headers['user-agent'] || 'unknown';

			const { user, token, refreshToken } = await signinUser({
				email,
				password,
			}, ip, userAgent);

			cookie.session.set({
				value: token,
				httpOnly: true,
				path: "/",
				sameSite: "lax",
				maxAge: 60 * 60 * 24 * 7,
			});

			// set.headers.set("Set-Cookie", cookie.session.toString());
			// set.headers.set("Set-Cookie", cookie.refresh.toString());

			return {
				success: true,
				message: "เข้าสู่ระบบเรียบร้อย",
				statusMessage: "สำเร็จ!",
				timestamp: new Date().toISOString(),
				data: { user, token, refreshToken },
			};
		},
		{
			body: signinSchema,
			response: authResponseUserSchema,
		},
	)

	.get(
		"/verify-email",
		async ({ query }) => {
			const token = query.token as string;
			const { safeUser } = await verifyEmail(token);
			return {
				success: true,
				message: "ยืนยันอีเมลสำเร็จ",
				statusMessage: "สำเร็จ!",
				data: { user: safeUser },
				timestamp: new Date().toISOString(),
			};
		},
		{
			response: messageResponseSchema,
		},
	)

	.post(
		"/forgot-password",
		async ({ body }) => {
			const { email } = body as { email: string };
			const { safeUser } = await forgotPassword(email);
			return {
				success: true,
				message: "ส่งอีเมลรีเซ็ตรหัสผ่านสำเร็จ",
				statusMessage: "สำเร็จ!",
				data: { user: safeUser },
				timestamp: new Date().toISOString(),
			};
		},
		{
			body: forgotPasswordSchema,
			response: messageResponseSchema,
		},
	)

	.post(
		"/reset-password",
		async ({ body }) => {
			const { token, newPassword } = body as {
				token: string;
				newPassword: string;
			};
			const { safeUser } = await resetPassword(token, newPassword);
			return {
				success: true,
				message: "รีเซ็ตรหัสผ่านสำเร็จ",
				statusMessage: "สำเร็จ!",
				data: { user: safeUser },
				timestamp: new Date().toISOString(),
			};
		},
		{
			body: resetPasswordSchema,
			response: messageResponseSchema,
		},
	)

	// ✅ Debug endpoint สำหรับตรวจสอบ Token Store (ใช้เฉพาะ development)
	.get("/debug/token-store", async () => {
		if (process.env.NODE_ENV !== 'development') {
			throw new HttpError(404, 'Not found');
		}

		const { getTokenStoreDebugInfo } = await import('@/core/utils/token-rotation');
		const debugInfo = getTokenStoreDebugInfo();

		return {
			success: true,
			message: 'Token store debug info',
			data: debugInfo
		};
	})

	.post("/refresh-token", async ({ body, headers }) => {
		try {

			console.log("refresh: body", body);
			const { refreshToken } = body as {
				refreshToken: string;
			};
			console.log("refresh: refreshTokenValue", refreshToken);

			// ✅ ดึง IP และ User Agent สำหรับ Token Rotation System
			const ip = headers['x-forwarded-for'] ||
				headers['cf-connecting-ip'] ||
				headers['x-real-ip'] ||
				'unknown';
			const userAgent = headers['user-agent'] || 'unknown';

			console.log("refresh: ip", ip);
			console.log("refresh: userAgent", userAgent);

			// ✅ Check rate limit for token refresh
			const { rateLimitService } = await import('@/core/services/rate-limit.service');
			const rateLimitCheck = rateLimitService.checkTokenRefreshLimit(ip, 'unknown'); // userId will be determined after validation

			if (!rateLimitCheck.allowed) {
				console.log("refresh: Rate limit exceeded");
				return {
					success: false,
					message: "Rate limit exceeded for token refresh",
					error: "TOO_MANY_REQUESTS",
					resetTime: rateLimitCheck.resetTime
				};
			}

			const { safeUser, newToken, newRefreshToken } =
				await refreshNewToken(refreshToken, userAgent, ip);
			return {
				success: true,
				message: "รีเฟรชโทเค็นเรียบร้อย",
				statusMessage: "สำเร็จ!",
				timestamp: new Date().toISOString(),
				data: {
					user: safeUser,
					token: newToken,
					refreshToken: newRefreshToken,
				},
			};

		} catch (error) {
			console.error("[refresh-token] Unexpected error:", error);
			// ถ้าเป็น HttpError ให้ throw ต่อ, ถ้าไม่ใช่ให้แปลงเป็น HttpError 500
			if (error instanceof HttpError) throw error;
			throw new HttpError(500, "เกิดข้อผิดพลาดในการรีเฟรชโทเค็น");
		}
	})

	// Routes ที่ต้องการ authentication
	.use(userAuthPlugin)
	.post("/signout", async ({ user, cookie, headers }: any) => {
		// ✅ ดึง token จาก Authorization header
		const authHeader = headers.authorization;
		const token = authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : undefined;

		const { safeUser } = await signoutUser(user?._id, token);

		cookie.session.remove();

		return {
			success: true,
			message: "ออกจากระบบสำเร็จ",
			statusMessage: "สำเร็จ!",
			timestamp: new Date().toISOString(),
			data: { user: safeUser },
		};
	})

	.get(
		"/profile",
		async ({ user }: any) => {
			const { safeUser } = await getUserProfile(user?._id);

			return {
				success: true,
				message: "ดึงข้อมูลโปรไฟล์สำเร็จ",
				statusMessage: "สำเร็จ!",
				timestamp: new Date().toISOString(),
				data: { user: safeUser }, 
			};
		},
		{
			response: userProfileResponseSchema,
		},
	)

	.put(
		"/profile",
		async ({ user, body }: any) => {
			const { firstName, lastName } = body as {
				firstName: string;
				lastName: string;
			};

			const { safeUser } = await updateUserProfile(user?._id, {
				firstName,
				lastName,
			});

			return {
				success: true,
				message: "อัปเดตโปรไฟล์สำเร็จ",
				statusMessage: "สำเร็จ!",
				timestamp: new Date().toISOString(),
				data: { user: safeUser },
			};
		},
		{
			body: t.Object({
				firstName: t.String(),
				lastName: t.String(),
			}),
			response: userProfileResponseSchema,
		},
	)

	.put(
		"/change-password",
		async ({ user, body }) => {
			const { currentPassword, newPassword } = body as {
				currentPassword: string;
				newPassword: string;
			};

			const data = await changePassword(
				user?._id ?? "",
				currentPassword,
				newPassword,
			);

			return {
				success: true,
				message: "เปลี่ยนรหัสผ่านสำเร็จ",
				statusMessage: "สำเร็จ!",
				timestamp: new Date().toISOString(),
				data,
			};
		},
		{
			body: changePasswordSchema,
		},
	)

	// Upload profile image
	.post(
		"/upload/profile",
		async ({ user, body }: any) => {
			const file = body.file;
			if (!file) throw new Error("ไม่พบไฟล์รูปภาพ");

			// แปลง file เป็น buffer
			const buffer = await file.arrayBuffer();
			const { user: safeUser, imageUrl } = await uploadUserProfileImage(
				user._id,
				Buffer.from(buffer),
			);
			return {
				success: true,
				message: "อัพโหลดรูปโปรไฟล์สำเร็จ",
				statusMessage: "สำเร็จ!",
				timestamp: new Date().toISOString(),
				data: { user: safeUser, imageUrl },
			};
		},
		{
			body: t.Object({
				file: t.File(),
			}),
		},
	)

	// Upload cover image
	.post(
		"/upload/cover",
		async ({ user, body }: any) => {
			const file = body.file;
			if (!file) throw new Error("ไม่พบไฟล์รูปภาพ");

			// แปลง file เป็น buffer
			const buffer = await file.arrayBuffer();
			const { user: safeUser, imageUrl } = await uploadUserCoverImage(
				user._id,
				Buffer.from(buffer),
			);
			return {
				success: true,
				message: "อัพโหลดรูปปกสำเร็จ",
				statusMessage: "สำเร็จ!",
				timestamp: new Date().toISOString(),
				data: { user: safeUser, imageUrl },
			};
		},
		{
			body: t.Object({
				file: t.File(),
			}),
		},
	)

	// Generate signed URL for client-side upload
	.get("/upload/signature/:type", async ({ user, params }: any) => {
		const { type } = params;
		if (type !== "profile" && type !== "cover") {
			throw new Error("ประเภทการอัพโหลดไม่ถูกต้อง");
		}

		const signedData = await generateUserUploadSignature(user._id, type);
		return {
			success: true,
			message: "สร้าง signed URL สำเร็จ",
			statusMessage: "สำเร็จ!",
			timestamp: new Date().toISOString(),
			data: signedData,
		};
	})

	// Upload profile image via base64
	.post(
		"/upload/profile/base64",
		async ({ user, body }: any) => {
			const { base64Image } = body;
			if (!base64Image) throw new Error("ไม่พบข้อมูล base64");

			const { user: safeUser, imageUrl } = await uploadUserProfileImage(
				user._id,
				base64Image,
			);
			return {
				success: true,
				message: "อัพโหลดรูปโปรไฟล์สำเร็จ",
				statusMessage: "สำเร็จ!",
				timestamp: new Date().toISOString(),
				data: { user: safeUser, imageUrl },
			};
		},
		{
			body: t.Object({
				base64Image: t.String(),
			}),
		},
	)

	// Upload cover image via base64
	.post(
		"/upload/cover/base64",
		async ({ user, body }: any) => {
			const { base64Image } = body;
			if (!base64Image) throw new Error("ไม่พบข้อมูล base64");

			const { user: safeUser, imageUrl } = await uploadUserCoverImage(
				user._id,
				base64Image,
			);
			return {
				success: true,
				message: "อัพโหลดรูปปกสำเร็จ",
				statusMessage: "สำเร็จ!",
				timestamp: new Date().toISOString(),
				data: { user: safeUser, imageUrl },
			};
		},
		{
			body: t.Object({
				base64Image: t.String(),
			}),
		},
	);
