
import crypto from 'crypto';

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: any) => string;
  handler?: (req: any, res: any) => void;
}

export interface RateLimitRecord {
  key: string;
  requests: number;
  resetTime: number;
  blocked: boolean;
  blockedUntil?: number;
}

export class RateLimitService {
  private static instance: RateLimitService;
  private rateLimitStore = new Map<string, RateLimitRecord>();

  // Default configurations
  private readonly DEFAULT_CONFIGS = {
    TOKEN_REFRESH: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 10, // 10 requests per 15 minutes
      skipSuccessfulRequests: false,
      skipFailedRequests: false
    },
    AUTH_ATTEMPTS: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 5, // 5 attempts per hour
      skipSuccessfulRequests: true,
      skipFailedRequests: false
    },
    API_GENERAL: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 requests per minute
      skipSuccessfulRequests: false,
      skipFailedRequests: false
    },
    TOKEN_ROTATION: {
      windowMs: 5 * 60 * 1000, // 5 minutes
      maxRequests: 3, // 3 rotations per 5 minutes
      skipSuccessfulRequests: false,
      skipFailedRequests: false
    }
  };

  private constructor() {
    // Cleanup expired rate limit records every 5 minutes
    setInterval(() => this.cleanupExpiredRecords(), 5 * 60 * 1000);
  }

  static getInstance(): RateLimitService {
    if (!RateLimitService.instance) {
      RateLimitService.instance = new RateLimitService();
    }
    return RateLimitService.instance;
  }

  /**
   * Check rate limit for a specific key and configuration
   */
  checkRateLimit(
    key: string,
    configType: keyof typeof this.DEFAULT_CONFIGS,
    customConfig?: Partial<RateLimitConfig>
  ): { allowed: boolean; remaining: number; resetTime: number; blocked: boolean } {
    const config = { ...this.DEFAULT_CONFIGS[configType], ...customConfig };
    const now = Date.now();
    
    // Get or create rate limit record
    let record = this.rateLimitStore.get(key);
    
    if (!record || now > record.resetTime) {
      // Create new record or reset existing one
      record = {
        key,
        requests: 0,
        resetTime: now + config.windowMs,
        blocked: false
      };
      this.rateLimitStore.set(key, record);
    }

    // Check if currently blocked
    if (record.blocked && record.blockedUntil && now < record.blockedUntil) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: record.blockedUntil,
        blocked: true
      };
    }

    // Reset block if time has passed
    if (record.blocked && record.blockedUntil && now >= record.blockedUntil) {
      record.blocked = false;
      record.blockedUntil = undefined;
    }

    // Check if limit exceeded
    if (record.requests >= config.maxRequests) {
      // Block for the remaining window time
      record.blocked = true;
      record.blockedUntil = record.resetTime;
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: record.blockedUntil,
        blocked: true
      };
    }

    // Increment request count
    record.requests++;

    return {
      allowed: true,
      remaining: Math.max(0, config.maxRequests - record.requests),
      resetTime: record.resetTime,
      blocked: false
    };
  }

  /**
   * Generate rate limit key based on IP and optional user ID
   */
  generateKey(ip: string, userId?: string, action?: string): string {
    const components = [ip];
    if (userId) components.push(userId);
    if (action) components.push(action);
    
    return crypto.createHash('sha256')
      .update(components.join(':'))
      .digest('hex')
      .substring(0, 16);
  }

  /**
   * Check token refresh rate limit
   */
  checkTokenRefreshLimit(ip: string, userId: string): { allowed: boolean; remaining: number; resetTime: number; blocked: boolean } {
    const key = this.generateKey(ip, userId, 'token_refresh');
    return this.checkRateLimit(key, 'TOKEN_REFRESH');
  }

  /**
   * Check authentication attempts rate limit
   */
  checkAuthAttemptsLimit(ip: string, email?: string): { allowed: boolean; remaining: number; resetTime: number; blocked: boolean } {
    const key = this.generateKey(ip, email, 'auth_attempts');
    return this.checkRateLimit(key, 'AUTH_ATTEMPTS');
  }

  /**
   * Check API general rate limit
   */
  checkApiGeneralLimit(ip: string): { allowed: boolean; remaining: number; resetTime: number; blocked: boolean } {
    const key = this.generateKey(ip, undefined, 'api_general');
    return this.checkRateLimit(key, 'API_GENERAL');
  }

  /**
   * Check token rotation rate limit
   */
  checkTokenRotationLimit(ip: string, userId: string): { allowed: boolean; remaining: number; resetTime: number; blocked: boolean } {
    const key = this.generateKey(ip, userId, 'token_rotation');
    return this.checkRateLimit(key, 'TOKEN_ROTATION');
  }

  /**
   * Reset rate limit for a specific key
   */
  resetRateLimit(key: string): void {
    this.rateLimitStore.delete(key);
  }

  /**
   * Get rate limit statistics
   */
  getRateLimitStats(): {
    totalRecords: number;
    blockedRecords: number;
    activeRecords: number;
  } {
    let totalRecords = 0;
    let blockedRecords = 0;
    let activeRecords = 0;

    for (const record of this.rateLimitStore.values()) {
      totalRecords++;
      if (record.blocked) {
        blockedRecords++;
      } else {
        activeRecords++;
      }
    }

    return { totalRecords, blockedRecords, activeRecords };
  }

  /**
   * Cleanup expired rate limit records
   */
  private cleanupExpiredRecords(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, record] of this.rateLimitStore.entries()) {
      // Remove if reset time has passed and not blocked
      if (now > record.resetTime && !record.blocked) {
        expiredKeys.push(key);
      }
      // Remove if blocked time has passed
      if (record.blocked && record.blockedUntil && now > record.blockedUntil) {
        expiredKeys.push(key);
      }
    }

    // Remove expired records
    for (const key of expiredKeys) {
      this.rateLimitStore.delete(key);
    }

    if (expiredKeys.length > 0) {
      console.log(`[RateLimitService] Cleaned up ${expiredKeys.length} expired rate limit records`);
    }
  }
}

export const rateLimitService = RateLimitService.getInstance(); 