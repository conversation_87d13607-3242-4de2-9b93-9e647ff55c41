import crypto from 'crypto';
import { RefreshToken, IRefreshToken } from '@/modules/user/refresh-token.model';
import { rateLimitService } from '@/core/services/rate-limit.service';
import { monitoringService } from '@/core/services/monitoring.service';

/**
 * ✅ Enhanced Backend Token Rotation System with Database Storage
 */

// Configuration
const TOKEN_CONFIG = {
    REFRESH_TOKEN_LENGTH: 64,
    MAX_ROTATION_COUNT: 10,
    TOKEN_LIFETIME: 30 * 24 * 60 * 60 * 1000, // 30 วัน - ระยะเวลาสูงสุดของ refresh token
    ROTATION_THRESHOLD: 24 * 60 * 60 * 1000, // 24 ชั่วโมง - ระยะเวลาก่อนจะ rotate token
    
    // การตั้งค่าเพิ่มเติม
    // TOKEN_LIFETIME: 7 * 24 * 60 * 60 * 1000, // 7 วัน (สำหรับความปลอดภัยสูง)
    // TOKEN_LIFETIME: 90 * 24 * 60 * 60 * 1000, // 90 วัน (สำหรับความสะดวก)
    // ROTATION_THRESHOLD: 12 * 60 * 60 * 1000, // 12 ชั่วโมง (rotate บ่อยขึ้น)
    // ROTATION_THRESHOLD: 7 * 24 * 60 * 60 * 1000, // 7 วัน (rotate น้อยลง)
} as const;

// ✅ Auto cleanup configuration
const CLEANUP_CONFIG = {
    // ลบ expired tokens ทุก 1 ชั่วโมง (default)
    CLEANUP_INTERVAL: 60 * 60 * 1000,
    
    // การตั้งค่าเพิ่มเติม
    // CLEANUP_INTERVAL: 30 * 60 * 1000, // ทุก 30 นาที (ลบเร็วขึ้น)
    // CLEANUP_INTERVAL: 2 * 60 * 60 * 1000, // ทุก 2 ชั่วโมง (ลบช้าลง)
    
    // ระยะเวลาก่อนลบ revoked tokens (7 วัน)
    REVOKED_TOKEN_CLEANUP_DELAY: 7 * 24 * 60 * 60 * 1000,
} as const;

/**
 * Generate secure refresh token
 */
export function generateRefreshToken(): { token: string; tokenId: string } {
    const token = crypto.randomBytes(TOKEN_CONFIG.REFRESH_TOKEN_LENGTH).toString('hex');
    const tokenId = crypto.randomUUID();

    return { token, tokenId };
}

/**
 * Hash refresh token for storage
 */
function hashRefreshToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
}

/**
 * Create device fingerprint
 */
export function createDeviceFingerprint(userAgent: string, ipAddress: string): string {
    return crypto.createHash('sha256')
        .update(`${userAgent}:${ipAddress}`)
        .digest('hex')
        .substring(0, 32);
}

/**
 * Store refresh token in database
 */
export async function storeRefreshToken(
    token: string,
    tokenId: string,
    userId: string,
    deviceFingerprint?: string,
    userAgent?: string,
    ipAddress?: string
): Promise<void> {
    const hashedToken = hashRefreshToken(token);
    const now = new Date();
    const expiresAt = new Date(now.getTime() + TOKEN_CONFIG.TOKEN_LIFETIME);

    const refreshTokenDoc = new RefreshToken({
        tokenId,
        userId,
        hashedToken,
        createdAt: now,
        lastUsed: now,
        rotationCount: 0,
        isRevoked: false,
        deviceFingerprint,
        userAgent,
        ipAddress,
        expiresAt
    });

    await refreshTokenDoc.save();

    // Log token activity
    monitoringService.logTokenActivity({
        userId,
        action: 'token_created',
        tokenId,
        deviceFingerprint,
        ipAddress: ipAddress || 'unknown',
        userAgent: userAgent || 'unknown',
        success: true,
        metadata: {
            tokenLength: token.length,
            expiresAt: expiresAt.toISOString()
        }
    });

    console.log('[storeRefreshToken] Token stored in database:', { 
        tokenId, 
        userId, 
        deviceFingerprint: deviceFingerprint?.substring(0, 16) + '...' 
    });
}

/**
 * Validate refresh token from database
 */
export async function validateRefreshToken(
    token: string,
    deviceFingerprint?: string,
    ipAddress?: string,
    userAgent?: string
): Promise<{ valid: boolean; record?: IRefreshToken; shouldRotate?: boolean }> {
    console.log('[validateRefreshToken] Validating token:', {
        tokenLength: token?.length,
        deviceFingerprint: deviceFingerprint?.substring(0, 16) + '...'
    });

    const hashedToken = hashRefreshToken(token);
    const now = new Date();

    // Find matching token in database
    const matchingRecord = await RefreshToken.findOne({
        hashedToken,
        isRevoked: false,
        expiresAt: { $gt: now }
    });

    if (!matchingRecord) {
        console.log('[validateRefreshToken] No matching token found');
        
        // Log failed validation
        monitoringService.logTokenActivity({
            userId: 'unknown',
            action: 'token_validation_failed',
            ipAddress: ipAddress || 'unknown',
            userAgent: userAgent || 'unknown',
            success: false,
            error: 'Token not found or expired'
        });

        return { valid: false };
    }

    console.log('[validateRefreshToken] Token found:', {
        tokenId: matchingRecord.tokenId,
        userId: matchingRecord.userId.toString(),
        rotationCount: matchingRecord.rotationCount
    });

    // Check device fingerprint if provided
    if (deviceFingerprint && matchingRecord.deviceFingerprint) {
        if (matchingRecord.deviceFingerprint !== deviceFingerprint) {
            console.log('[validateRefreshToken] Device fingerprint mismatch - revoking all user tokens');
            
            // Log security event
            monitoringService.logSecurityEvent({
                type: 'device_mismatch',
                severity: 'high',
                userId: matchingRecord.userId.toString(),
                ipAddress: ipAddress || 'unknown',
                userAgent: userAgent || 'unknown',
                description: `Device fingerprint mismatch detected for user ${matchingRecord.userId}`,
                metadata: {
                    expectedFingerprint: matchingRecord.deviceFingerprint.substring(0, 16) + '...',
                    receivedFingerprint: deviceFingerprint.substring(0, 16) + '...'
                }
            });

            // Revoke all tokens for security
            await revokeAllUserTokens(matchingRecord.userId.toString());
            
            return { valid: false };
        }
    }

    // Check if rotation is needed
    const timeSinceLastUsed = now.getTime() - matchingRecord.lastUsed.getTime();
    const shouldRotate = (
        timeSinceLastUsed > TOKEN_CONFIG.ROTATION_THRESHOLD ||
        matchingRecord.rotationCount >= TOKEN_CONFIG.MAX_ROTATION_COUNT
    );

    console.log('[validateRefreshToken] Should rotate:', shouldRotate);

    // Update last used
    matchingRecord.lastUsed = now;
    await matchingRecord.save();

    // Log successful validation
    monitoringService.logTokenActivity({
        userId: matchingRecord.userId.toString(),
        action: 'token_refreshed',
        tokenId: matchingRecord.tokenId,
        deviceFingerprint,
        ipAddress: ipAddress || 'unknown',
        userAgent: userAgent || 'unknown',
        success: true,
        metadata: {
            shouldRotate,
            rotationCount: matchingRecord.rotationCount
        }
    });

    return {
        valid: true,
        record: matchingRecord,
        shouldRotate
    };
}

/**
 * Rotate refresh token in database
 */
export async function rotateRefreshToken(
    oldTokenId: string,
    userId: string,
    deviceFingerprint?: string,
    userAgent?: string,
    ipAddress?: string
): Promise<{ token: string; tokenId: string } | null> {
    console.log('[rotateRefreshToken] Rotating token:', { oldTokenId, userId });

    // Check rate limit for token rotation
    const rateLimitCheck = rateLimitService.checkTokenRotationLimit(ipAddress || 'unknown', userId);
    if (!rateLimitCheck.allowed) {
        console.log('[rotateRefreshToken] Rate limit exceeded for token rotation');
        
        monitoringService.logSecurityEvent({
            type: 'rate_limit_exceeded',
            severity: 'medium',
            userId,
            ipAddress: ipAddress || 'unknown',
            userAgent: userAgent || 'unknown',
            description: `Token rotation rate limit exceeded for user ${userId}`,
            metadata: {
                action: 'token_rotation',
                remaining: rateLimitCheck.remaining,
                resetTime: rateLimitCheck.resetTime
            }
        });

        return null;
    }

    const oldRecord = await RefreshToken.findOne({ tokenId: oldTokenId, isRevoked: false });
    if (!oldRecord) {
        console.log('[rotateRefreshToken] Old token not found or revoked');
        return null;
    }

    // Generate new token
    const { token: newToken, tokenId: newTokenId } = generateRefreshToken();

    // Store new token
    await storeRefreshToken(newToken, newTokenId, userId, deviceFingerprint, userAgent, ipAddress);

    // Update rotation count for new token
    const newRecord = await RefreshToken.findOne({ tokenId: newTokenId });
    if (newRecord) {
        newRecord.rotationCount = oldRecord.rotationCount + 1;
        await newRecord.save();
    }

    // Revoke old token
    await revokeRefreshToken(oldTokenId);

    // Log token rotation
    monitoringService.logTokenActivity({
        userId,
        action: 'token_rotated',
        tokenId: newTokenId,
        deviceFingerprint,
        ipAddress: ipAddress || 'unknown',
        userAgent: userAgent || 'unknown',
        success: true,
        metadata: {
            oldTokenId,
            newTokenId,
            rotationCount: newRecord?.rotationCount || 0
        }
    });

    console.log('[rotateRefreshToken] Token rotated successfully:', {
        newTokenId,
        rotationCount: newRecord?.rotationCount || 0
    });

    return { token: newToken, tokenId: newTokenId };
}

/**
 * Revoke refresh token in database
 */
export async function revokeRefreshToken(tokenId: string): Promise<void> {
    const record = await RefreshToken.findOne({ tokenId });
    if (record) {
        record.isRevoked = true;
        record.revokedAt = new Date();
        await record.save();
        
        console.log('[revokeRefreshToken] Token revoked:', tokenId);
    }
}

/**
 * Revoke all tokens for a user in database
 */
export async function revokeAllUserTokens(userId: string): Promise<void> {
    const result = await RefreshToken.updateMany(
        { userId, isRevoked: false },
        { 
            isRevoked: true, 
            revokedAt: new Date() 
        }
    );
    
    console.log('[revokeAllUserTokens] Revoked tokens for user:', { userId, count: result.modifiedCount });
}

/**
 * Cleanup expired tokens from database
 */
export async function cleanupExpiredTokens(): Promise<void> {
    const now = new Date();
    const cutoffTime = new Date(now.getTime() - CLEANUP_CONFIG.REVOKED_TOKEN_CLEANUP_DELAY);

    // Delete expired tokens
    const expiredResult = await RefreshToken.deleteMany({
        expiresAt: { $lt: now }
    });

    // Delete old revoked tokens
    const revokedResult = await RefreshToken.deleteMany({
        isRevoked: true,
        revokedAt: { $lt: cutoffTime }
    });

    if (expiredResult.deletedCount > 0 || revokedResult.deletedCount > 0) {
        console.log('[cleanupExpiredTokens] Cleaned up tokens:', {
            expired: expiredResult.deletedCount,
            revoked: revokedResult.deletedCount
        });
    }
}

/**
 * Get user token statistics from database
 */
export async function getUserTokenStats(userId: string): Promise<{
    activeTokens: number;
    revokedTokens: number;
    totalRotations: number;
}> {
    const [activeTokens, revokedTokens] = await Promise.all([
        RefreshToken.countDocuments({ userId, isRevoked: false }),
        RefreshToken.countDocuments({ userId, isRevoked: true })
    ]);

    const activeTokenRecords = await RefreshToken.find({ userId, isRevoked: false });
    const totalRotations = activeTokenRecords.reduce((sum, record) => sum + record.rotationCount, 0);

    return { activeTokens, revokedTokens, totalRotations };
}

/**
 * Get token store debug info from database
 */
export async function getTokenStoreDebugInfo() {
    const totalTokens = await RefreshToken.countDocuments();
    const activeTokens = await RefreshToken.countDocuments({ isRevoked: false });
    const revokedTokens = await RefreshToken.countDocuments({ isRevoked: true });

    const recentTokens = await RefreshToken.find({ isRevoked: false })
        .sort({ createdAt: -1 })
        .limit(10)
        .select('tokenId userId createdAt deviceFingerprint');

    return {
        totalTokens,
        activeTokens,
        revokedTokens,
        recentTokens: recentTokens.map(token => ({
            tokenId: token.tokenId,
            userId: token.userId.toString(),
            createdAt: token.createdAt.toISOString(),
            deviceFingerprint: token.deviceFingerprint?.substring(0, 16) + '...'
        }))
    };
}

// Cleanup expired tokens according to configuration
setInterval(cleanupExpiredTokens, CLEANUP_CONFIG.CLEANUP_INTERVAL);