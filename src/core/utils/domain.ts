import { Site } from "@/modules/site/site.model";
import { HttpError } from "./error";

export async function checkDomainAvailability(
	fullDomain: string,
): Promise<any> {
	const available = await Site.findOne({
		fullDomain: fullDomain,
	}).select("fullDomain");
	// console.log("checkDomainAvailability: available", available);

	return {
		available: !available,
		fullDomain,
	};
}

export async function registerDomainVercel(fullDomain: any): Promise<any> {
	try {
		const response = await fetch(
			`https://api.vercel.com/v9/domains/${fullDomain}/config`,
			{
				method: "GET",
				headers: {
					Authorization: `Bearer ${Bun.env.VC_TOKEN}`,
				},
			},
		);
		const resCheck = await response.json();

		if (resCheck?.configuredBy) {
			throw new HttpError(
				400,
				"ไม่สามารถสร้างที่อยู่เว็บไซต์ได้ ที่อยู่เว็บไซต์อาจถูกใช้งานแล้ว!",
			);
		}

		let resRegisterDomain: any = null;

		// console.log(Bun.env.NODE_ENV)

		if (Bun.env.NODE_ENV !== "development") {
			const resRegister: any = await fetch(
				`https://api.vercel.com/v9/projects/${Bun.env.VC_PROJECT_ID_IS1SHOP}/domains`,
				{
					method: "POST",
					headers: {
						Authorization: `Bearer ${Bun.env.VC_TOKEN}`,
					},
					body: JSON.stringify({ name: fullDomain }),
				},
			);
			resRegisterDomain = await resRegister.json();


			if (!resRegisterDomain?.ok) {
				throw new HttpError(
					resRegisterDomain?.status || 409,
					"โดเมนนี้ถูกใช้งานบนเซิร์ฟเวอร์แล้ว",
				);
			}


			if (resRegisterDomain?.error) {
				throw new HttpError(
					400,
					resRegisterDomain.error.message || "ไม่สามารถเพิ่มโดเมนนี้ได้",
				);
			}

		}


		return {
			resRegisterDomain,
			resCheck,
		};
	} catch (err: any) {
		console.error("[registerDomainVercel] Unexpected error:", err);
		// ถ้าเป็น HttpError ให้ throw ต่อ, ถ้าไม่ใช่ให้แปลงเป็น HttpError 500
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, "เกิดข้อผิดพลาดในการสร้างโดเมน");
	}
}

// Detailed server-side domain validation and processing
export async function getFullDomain(
	typeDomain: string,
	subDomain: string,
	mainDomain: string,
	customDomain: string,
) {
	console.log("getFullDomain", typeDomain, subDomain, mainDomain, customDomain);

	// Validate domain type
	if (!typeDomain) {
		throw new HttpError(400, "กรุณาระบุประเภทโดเมน");
	}

	if (!['subdomain', 'custom'].includes(typeDomain)) {
		throw new HttpError(400, "ประเภทโดเมนไม่ถูกต้อง ต้องเป็น subdomain หรือ custom");
	}

	let fullDomain: string;

	if (typeDomain === "subdomain") {
		// Validate subdomain
		if (!subDomain) {
			throw new HttpError(400, "กรุณาระบุซับโดเมน");
		}
		if (subDomain.length < 3 || subDomain.length > 20) {
			throw new HttpError(400, "ซับโดเมนต้องมีความยาว 3-20 ตัวอักษร");
		}
		if (!/^[a-z0-9-]+$/.test(subDomain)) {
			throw new HttpError(400, "ซับโดเมนต้องเป็นตัวอักษรภาษาอังกฤษ ตัวเลข และขีดเท่านั้น");
		}
		if (subDomain.startsWith('-') || subDomain.endsWith('-')) {
			throw new HttpError(400, "ซับโดเมนไม่สามารถขึ้นต้นหรือลงท้ายด้วยขีดได้");
		}

		// Validate main domain
		if (!mainDomain) {
			throw new HttpError(400, "กรุณาระบุโดเมนหลัก");
		}

		// Create full domain
		if (subDomain.includes(".")) {
			fullDomain = subDomain;
		} else {
			fullDomain = `${subDomain}.${mainDomain}`;
		}
	} else if (typeDomain === "custom") {
		// Validate custom domain
		if (!customDomain || customDomain === "undefined") {
			throw new HttpError(400, "กรุณาระบุโดเมนที่ต้องการ");
		}
		if (customDomain.length < 4 || customDomain.length > 100) {
			throw new HttpError(400, "โดเมนต้องมีความยาว 4-100 ตัวอักษร");
		}
		if (!/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/.test(customDomain)) {
			throw new HttpError(400, "รูปแบบโดเมนไม่ถูกต้อง");
		}
		fullDomain = customDomain;
	}

	// Development environment adjustment
	if (Bun.env.NODE_ENV !== "production") {
		fullDomain = `${fullDomain.split(".")[0]}.localhost:8080`;
	}

	return fullDomain;
}
