{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "close": "Close", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "and": "and"}, "auth": {"login": "<PERSON><PERSON>", "signin": "Sign In", "signinDescription": "Sign in to access your dashboard", "signup": "Sign Up", "signupDescription": "Create a new account to get started", "signout": "Signout", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "registerSuccess": "Registration successful", "registerError": "Registration failed", "noAccount": "Don't have an account?", "hasAccount": "Already have an account?", "signingIn": "Signing in...", "signingUp": "Signing up...", "agreeToTerms": "I agree to the", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "forgotPasswordTitle": "Forgot Password", "forgotPasswordDescription": "Please enter your registered email address. We will send you a password reset link.", "sendResetEmail": "Send Reset Email", "rememberPassword": "Remember your password?", "resetPasswordTitle": "Reset Password", "resetPasswordDescription": "Please enter your new password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "resetPassword": "Reset Password", "resettingPassword": "Resetting password...", "backToSignin": "Back to Sign In", "or": "or", "help": "Help", "topup": "Top Up", "verifyEmailTitle": "<PERSON><PERSON><PERSON>", "verifyEmailDescription": "Verify your email to activate your account", "verifyEmailToken": "Email Verification Token", "verifyEmailTokenPlaceholder": "Enter token received from email", "verifyEmail": "<PERSON><PERSON><PERSON>", "verifyingEmail": "Verifying email...", "verifyEmailSuccess": "Email verification successful", "verifyEmailError": "Email verification failed", "redirectingToSignin": "Redirecting to sign in page...", "tokenInvalid": "Token is invalid or expired", "connectionError": "Connection error occurred", "resendVerification": "Resend Verification Email", "noVerificationEmail": "Didn't receive verification email?"}, "home": {"title": "Complete Website Rental System", "description": "Rent websites that suit your needs with easy and secure management", "subtitle": "Everything you need for website rental", "subtitleDescription": "Our website rental system comes with comprehensive features, easy to use and secure", "autoTopup": "Automatic Top-up", "autoTopupDetail": "Supports top-up system through Gift Wallet, QR Code scanning, Redeam and admin top-up", "multiProduct": "Multi-Product Support", "multiProductDetail": "Supports selling various types of products such as top-up cards, phone numbers, game IDs, app IDs, various codes and many more", "support": "Excellent Support & Help", "supportDetail": "Frequently asked questions, getting started guide, Discord group and Facebook page support to help and answer questions as quickly as possible"}, "terms": {"title": "Terms of Service", "description": "Terms of service for complete website rental system", "lastUpdated": "Last Updated", "updateDate": "January 1, 2024", "acceptance": "Acceptance of Terms", "acceptanceText": "By using our service, you agree to all terms and conditions stated on this page", "serviceDescription": "Service Description", "serviceDescriptionText": "We provide a complete website rental system that includes:", "serviceFeature1": "Online website management system", "serviceFeature2": "Product and category management system", "serviceFeature3": "Automatic payment system", "serviceFeature4": "Member and profile system", "userObligations": "User Obligations", "userObligationsText": "Users are obligated to:", "obligation1": "Provide accurate and complete information", "obligation2": "Not use the service for illegal purposes", "obligation3": "Not share login credentials with others", "obligation4": "Report immediately if unusual activity is detected", "obligation5": "Comply with applicable laws", "payment": "Payment", "paymentText": "Payment terms:", "payment1": "Service fees will be charged according to the selected package", "payment2": "Payment will be processed through designated channels", "payment3": "Non-payment will result in service suspension", "privacy": "Privacy", "privacyText": "Personal data management will be conducted according to our privacy policy", "termination": "Service Termination", "terminationText": "We reserve the right to terminate service for users who violate terms of service", "liability": "Liability", "liabilityText": "We will not be liable for damages caused by improper use or violation of terms", "changes": "Changes", "changesText": "We reserve the right to modify terms of service with prior notice", "contact": "Contact Us", "contactText": "If you have questions about terms of service, please contact us:", "email": "Email", "phone": "Phone"}, "user": {"profile": "Profile", "users": "Users", "userManagement": "User Management", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "firstName": "First Name", "lastName": "Last Name", "username": "Username", "role": "Role", "status": "Status", "active": "Active", "inactive": "Inactive", "admin": "Administrator", "user": "User", "moderator": "Moderator", "createdAt": "Created At", "updatedAt": "Updated At", "lastLogin": "Last Login"}, "navigation": {"dashboard": "Dashboard", "users": "Users", "settings": "Settings", "reports": "Reports", "analytics": "Analytics", "website": "Website", "content": "Content", "pages": "Pages", "menu": "<PERSON><PERSON>", "themes": "Themes", "plugins": "Plugins"}, "website": {"websiteManagement": "Website Management", "createWebsite": "Create Website", "editWebsite": "Edit Website", "deleteWebsite": "Delete Website", "websiteName": "Website Name", "websiteUrl": "Website URL", "description": "Description", "template": "Template", "domain": "Domain", "ssl": "SSL", "backup": "Backup", "restore": "Rest<PERSON>"}, "theme": {"light": "Light", "dark": "Dark", "auto": "Auto", "currentTheme": "Current Theme", "toggleTheme": "Toggle Theme", "switchToLight": "Switch to Light Mode", "switchToDark": "Switch to Dark Mode", "switchToAuto": "Switch to Auto Mode", "systemPreference": "System Preference", "themeChanged": "Theme Changed", "followSystem": "Follow System Settings"}, "privacy": {"title": "Privacy Policy", "description": "Privacy policy for complete website rental system", "lastUpdated": "Last Updated", "updateDate": "January 1, 2024", "introduction": "Introduction", "introductionText": "We value the protection of your personal data. This policy explains how we collect, use, and protect your information", "informationCollected": "Information We Collect", "informationCollectedText": "We may collect the following information:", "info1": "Personal information such as name, email, phone number", "info2": "Website and system usage data", "info3": "Payment and transaction data", "info4": "Device and technology information", "info5": "Contact and support information", "howWeUse": "How We Use Information", "howWeUseText": "We use your information to:", "use1": "Provide service and customer support", "use2": "Improve and develop services", "use3": "Send important service information", "use4": "Comply with legal requirements", "informationSharing": "Information Sharing", "informationSharingText": "We do not sell, exchange, or transfer your personal information to third parties without your consent, except where required by law", "dataSecurity": "Data Security", "dataSecurityText": "We use appropriate security measures to protect your information:", "security1": "Encryption of sensitive data", "security2": "Strict access controls", "security3": "Regular security audits", "cookies": "Cookies", "cookiesText": "We use cookies to improve your user experience. You can control cookie usage through browser settings", "yourRights": "Your Rights", "yourRightsText": "You have the right to:", "right1": "Access and view your personal information", "right2": "Correct or update inaccurate information", "right3": "Request deletion of your personal information", "right4": "Object to processing of your information", "changes": "Changes", "changesText": "We may update this privacy policy from time to time. Any changes will be announced on this page", "contact": "Contact Us", "contactText": "If you have questions about privacy policy, please contact us:", "email": "Email", "phone": "Phone"}}