import { BaseService } from './base';
import type { ApiResponse } from '$lib/types/common';

// ✅ Analytics Types
export interface SalesAnalytics {
    today: number;
    month: number;
    total: number;
    todayChange: string;
    monthChange: string;
    chartData: number[];
    topProducts?: ProductAnalytics[];
    topCategories?: CategoryAnalytics[];
}

export interface ProductAnalytics {
    id: string;
    name: string;
    sales: number;
    revenue: number;
    change: string;
}

export interface CategoryAnalytics {
    id: string;
    name: string;
    sales: number;
    revenue: number;
    change: string;
}

export interface AnalyticsParams {
    period?: 'today' | 'week' | 'month' | 'year' | 'custom';
    startDate?: string;
    endDate?: string;
    siteId?: string;
}

export interface ExportConfig {
    period?: string;
    startDate?: string;
    endDate?: string;
    format?: 'csv' | 'excel' | 'pdf';
}

/**
 * ✅ Analytics Service - ใช้ BaseService และ native fetch
 * - Type-safe analytics data
 * - Consistent error handling
 * - Parameter validation
 */
class AnalyticsService extends BaseService {

    /**
     * ✅ Validate analytics parameters
     */
    private validateAnalyticsParams(params: AnalyticsParams): string | null {
        if (params.period === 'custom') {
            if (!params.startDate || !params.endDate) {
                return 'กรุณาระบุวันที่เริ่มต้นและสิ้นสุดสำหรับช่วงเวลาที่กำหนดเอง';
            }

            const startDate = new Date(params.startDate);
            const endDate = new Date(params.endDate);

            if (startDate > endDate) {
                return 'วันที่เริ่มต้นต้องไม่เกินวันที่สิ้นสุด';
            }
        }

        return null;
    }

    /**
     * ✅ Build query parameters
     */
    private buildQueryParams(params: AnalyticsParams): string {
        const queryParams = new URLSearchParams();

        if (params.period) queryParams.append('period', params.period);
        if (params.startDate) queryParams.append('startDate', params.startDate);
        if (params.endDate) queryParams.append('endDate', params.endDate);

        return queryParams.toString();
    }

    /**
     * ✅ Get sales analytics
     */
    async getSalesAnalytics(
        siteId: string,
        token: string,
        params: AnalyticsParams = {}
    ): Promise<ApiResponse<SalesAnalytics>> {
        if (!siteId?.trim()) {
            return {
                success: false,
                error: 'Site ID ไม่ถูกต้อง'
            };
        }

        if (!token?.trim()) {
            return {
                success: false,
                error: 'Token ไม่ถูกต้อง'
            };
        }

        // Validate parameters
        const validationError = this.validateAnalyticsParams(params);
        if (validationError) {
            return {
                success: false,
                error: validationError
            };
        }

        return this.handleRequest(async () => {
            const queryString = this.buildQueryParams(params);
            const endpoint = `/analytics/dashboard/${siteId}/sales${queryString ? `?${queryString}` : ''}`;

            const result = await this.makeAuthenticatedRequest<{ data: SalesAnalytics }>(endpoint, token);
            return result.data;
        });
    }

    /**
     * ✅ Get product analytics
     */
    async getProductAnalytics(
        siteId: string,
        token: string,
        params: AnalyticsParams = {}
    ): Promise<ApiResponse<ProductAnalytics[]>> {
        if (!siteId?.trim()) {
            return {
                success: false,
                error: 'Site ID ไม่ถูกต้อง'
            };
        }

        if (!token?.trim()) {
            return {
                success: false,
                error: 'Token ไม่ถูกต้อง'
            };
        }

        // Validate parameters
        const validationError = this.validateAnalyticsParams(params);
        if (validationError) {
            return {
                success: false,
                error: validationError
            };
        }

        return this.handleRequest(async () => {
            const queryString = this.buildQueryParams(params);
            const endpoint = `/product-analytics${queryString ? `?${queryString}` : ''}`;

            const result = await this.makeAuthenticatedRequest<{ data: ProductAnalytics[] }>(endpoint, token);
            return result.data;
        });
    }

    /**
     * ✅ Get category analytics
     */
    async getCategoryAnalytics(
        siteId: string,
        token: string,
        params: AnalyticsParams = {}
    ): Promise<ApiResponse<CategoryAnalytics[]>> {
        if (!siteId?.trim()) {
            return {
                success: false,
                error: 'Site ID ไม่ถูกต้อง'
            };
        }

        if (!token?.trim()) {
            return {
                success: false,
                error: 'Token ไม่ถูกต้อง'
            };
        }

        // Validate parameters
        const validationError = this.validateAnalyticsParams(params);
        if (validationError) {
            return {
                success: false,
                error: validationError
            };
        }

        return this.handleRequest(async () => {
            const queryString = this.buildQueryParams(params);
            const endpoint = `/product-analytics/category-performance${queryString ? `?${queryString}` : ''}`;

            const result = await this.makeAuthenticatedRequest<{ data: CategoryAnalytics[] }>(endpoint, token);
            return result.data;
        });
    }

    /**
     * ✅ Export analytics data
     */
    async exportAnalytics(
        siteId: string,
        token: string,
        config: ExportConfig
    ): Promise<ApiResponse<{ downloadUrl: string }>> {
        if (!siteId?.trim()) {
            return {
                success: false,
                error: 'Site ID ไม่ถูกต้อง'
            };
        }

        if (!token?.trim()) {
            return {
                success: false,
                error: 'Token ไม่ถูกต้อง'
            };
        }

        // Validate export config
        if (!config.format || !['csv', 'excel', 'pdf'].includes(config.format)) {
            return {
                success: false,
                error: 'รูปแบบการส่งออกไม่ถูกต้อง (csv, excel, pdf)'
            };
        }

        return this.handleRequest(async () => {
            const queryParams = new URLSearchParams();
            if (config.period) queryParams.append('period', config.period);
            if (config.startDate) queryParams.append('startDate', config.startDate);
            if (config.endDate) queryParams.append('endDate', config.endDate);
            if (config.format) queryParams.append('format', config.format);

            const queryString = queryParams.toString();
            const endpoint = `/analytics/dashboard/${siteId}/export${queryString ? `?${queryString}` : ''}`;

            const result = await this.makeAuthenticatedRequest<{ data: { downloadUrl: string } }>(endpoint, token);
            return result.data;
        });
    }
}

export const analyticsService = new AnalyticsService(); 