import { apiClient, createAuthHeaders } from '$lib/api/client';
import type { ApiResponse } from '$lib/types/common';
import { <PERSON>rror<PERSON><PERSON><PERSON> } from '$lib/utils/error-handler';

/**
 * ✅ Base Service Class - ใช้ร่วมกันทุก service
 * - Centralized error handling
 * - Consistent response format
 * - Reusable auth headers
 */
export abstract class BaseService {

    /**
     * ✅ Unified error handling - ใช้ method เดียวสำหรับทุก request
     */
    protected async handleRequest<T>(
        requestFn: () => Promise<T | { data: T, message?: string }>,
        defaultMessage?: string
    ): Promise<ApiResponse<T>> {
        try {
            const result = await requestFn();

            // ตรวจสอบว่า result มี data property หรือไม่
            if (result && typeof result === 'object' && 'data' in result) {
                return {
                    success: true,
                    data: result.data,
                    message: result.message || defaultMessage
                };
            }

            // ถ้าไม่มี data property แสดงว่าเป็น direct data
            return {
                success: true,
                data: result as T,
                message: defaultMessage
            };
        } catch (error) {
            // ใช้ ErrorHandler สำหรับการจัดการ error ที่สอดคล้องกัน
            const errorMessage = ErrorHandler.handleServiceError(error, this.constructor.name);
            
            return {
                success: false,
                error: errorMessage
            };
        }
    }

    /**
     * Create authenticated API call
     */
    protected async makeAuthenticatedRequest<T>(
        endpoint: string,
        token: string,
        options: Omit<import('$lib/api/client').ApiClientOptions, 'headers'> & { headers?: Record<string, string> } = {}
    ): Promise<T> {
        return apiClient.request<T>(endpoint, {
            ...options,
            headers: {
                ...createAuthHeaders(token),
                ...options.headers
            }
        });
    }

    /**
     * Create public API call (no auth required)
     */
    protected async makePublicRequest<T>(
        endpoint: string,
        options: import('$lib/api/client').ApiClientOptions = {}
    ): Promise<T> {
        return apiClient.request<T>(endpoint, options);
    }

    /**
     * Validate required fields
     */
    protected validateRequired(data: Record<string, any>, fields: string[]): string | null {
        for (const field of fields) {
            if (!data[field] || (typeof data[field] === 'string' && !data[field].trim())) {
                return `กรุณากรอก${this.getFieldLabel(field)}`;
            }
        }
        return null;
    }

    /**
     * Get Thai field label for validation messages
     */
    private getFieldLabel(field: string): string {
        const labels: Record<string, string> = {
            email: 'อีเมล',
            password: 'รหัสผ่าน',
            name: 'ชื่อ',
            firstName: 'ชื่อจริง',
            lastName: 'นามสกุล',
            phone: 'เบอร์โทรศัพท์',
            confirmPassword: 'ยืนยันรหัสผ่าน'
        };
        return labels[field] || field;
    }
}