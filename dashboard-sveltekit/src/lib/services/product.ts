import { BaseService } from './base';
import {
  validateCreateProductData,
  validateUpdateProductData,
  validateProductStockData,
  sanitizeProductData,
  type CreateProductData,
  type UpdateProductData,
  type ProductStockData
} from '$lib/schemas/product.schema';
import type { Product, ProductListResponse, ProductResponse, ProductStatsResponse } from '$lib/types/product';
import type { ApiResponse } from '$lib/types/common';

/**
 * ✅ Product Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class ProductService extends BaseService {

  /**
   * ✅ Get products with pagination and filters
   */
  async getProducts(siteId: string, token: string, params?: {
    page?: number;
    limit?: number;
    search?: string;
    categoryId?: string;
    status?: 'active' | 'inactive';
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<ApiResponse<ProductListResponse>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.categoryId) queryParams.append('categoryId', params.categoryId);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const result = await this.makeAuthenticatedRequest<{ data: ProductListResponse }>(
        `/product/dashboard/${siteId}/list?${queryParams}`,
        token
      );
      return result.data;
    });
  }

  /**
   * ✅ Get single product by ID
   */
  async getProduct(productId: string, siteId: string, token: string): Promise<ApiResponse<Product>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!productId?.trim()) {
      return {
        success: false,
        error: 'Product ID ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Product }>(
        `/product/dashboard/${siteId}/detail/${productId}`,
        token
      );
      return result.data;
    });
  }

  /**
   * ✅ Create new product
   */
  async createProduct(data: CreateProductData, siteId: string, token: string): Promise<ApiResponse<Product>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    // Validate input
    const sanitizedData = sanitizeProductData(data);
    const validation = validateCreateProductData(sanitizedData);

    if (!validation.success) {
      return {
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Product }>(
        `/product/dashboard/${siteId}/create`,
        token,
        {
          method: 'POST',
          body: sanitizedData
        }
      );
      return result.data;
    });
  }

  /**
   * ✅ Update existing product
   */
  async updateProduct(productId: string, data: UpdateProductData, siteId: string, token: string): Promise<ApiResponse<Product>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!productId?.trim()) {
      return {
        success: false,
        error: 'Product ID ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    // Validate input
    const sanitizedData = sanitizeProductData(data);
    const validation = validateUpdateProductData(sanitizedData);

    if (!validation.success) {
      return {
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Product }>(
        `/product/dashboard/${siteId}/update/${productId}`,
        token,
        {
          method: 'PUT',
          body: sanitizedData
        }
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete product
   */
  async deleteProduct(productId: string, siteId: string, token: string): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!productId?.trim()) {
      return {
        success: false,
        error: 'Product ID ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/product/dashboard/${siteId}/delete/${productId}`,
        token,
        {
          method: 'DELETE'
        }
      );
    });
  }

  /**
   * ✅ Get product statistics
   */
  async getProductStats(siteId: string, token: string): Promise<ApiResponse<ProductStatsResponse>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: ProductStatsResponse }>(
        `/product/dashboard/${siteId}/stats`,
        token
      );
      return result.data;
    });
  }

  /**
   * ✅ Update product stock
   */
  async updateProductStock(productId: string, data: ProductStockData, siteId: string, token: string): Promise<ApiResponse<Product>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!productId?.trim()) {
      return {
        success: false,
        error: 'Product ID ไม่ถูกต้อง'
      };
    }

    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง'
      };
    }

    // Validate input
    const validation = validateProductStockData(data);
    if (!validation.success) {
      return {
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Product }>(
        `/product/dashboard/${siteId}/stock/${productId}`,
        token,
        {
          method: 'PUT',
          body: data
        }
      );
      return result.data;
    });
  }
}

export const productService = new ProductService(); 