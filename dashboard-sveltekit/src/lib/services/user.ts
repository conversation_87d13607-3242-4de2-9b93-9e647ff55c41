import { BaseService } from './base';
import {
  validateUpdateProfileData,
  validateChangePasswordData,
  validateAvatarFile,
  sanitizeUserData,
  type UpdateProfileData,
  type ChangePasswordData,
  type UpdateAvatarData
} from '$lib/schemas/user.schema';
import type { User, UpdateUserData } from '$lib/types/user';
import type { ApiResponse } from '$lib/types/common';

/**
 * ✅ User Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class UserService extends BaseService {

  /**
   * ✅ Get current user profile
   */
  async getCurrentUser(token: string): Promise<ApiResponse<User>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: User }>('/user/profile', token);
      return result.data;
    });
  }

  /**
   * ✅ Update user profile
   */
  async updateProfile(data: UpdateProfileData, token: string): Promise<ApiResponse<User>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    // Validate input
    const sanitizedData = sanitizeUserData(data);
    const validationError = validateUpdateProfileData(sanitizedData);

    if (validationError) {
      return {
        success: false,
        error: validationError
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: User }>('/user/profile', token, {
        method: 'PUT',
        body: sanitizedData
      });
      return result.data;
    });
  }

  /**
   * ✅ Update user avatar
   */
  async updateAvatar(data: UpdateAvatarData, token: string): Promise<ApiResponse<{ avatar: string }>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    // Validate file
    const validationError = validateAvatarFile(data.avatar);
    if (validationError) {
      return {
        success: false,
        error: validationError
      };
    }

    return this.handleRequest(async () => {
      const formData = new FormData();
      formData.append('avatar', data.avatar);

      const result = await this.makeAuthenticatedRequest<{ data: { avatar: string } }>('/user/avatar', token, {
        method: 'POST',
        body: formData,
        headers: {
          // Remove Content-Type to let browser set it for FormData
          'Content-Type': undefined as any
        }
      });
      return result.data;
    });
  }

  /**
   * ✅ Change password
   */
  async changePassword(data: ChangePasswordData, token: string): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    // Validate input
    const sanitizedData = sanitizeUserData(data);
    const validationError = validateChangePasswordData(sanitizedData);

    if (validationError) {
      return {
        success: false,
        error: validationError
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>('/user/change-password', token, {
        method: 'POST',
        body: {
          currentPassword: sanitizedData.currentPassword,
          newPassword: sanitizedData.newPassword
        }
      });
    });
  }

  /**
   * ✅ Verify email with token
   */
  async verifyEmail(verificationToken: string): Promise<ApiResponse<void>> {
    if (!verificationToken?.trim()) {
      return {
        success: false,
        error: 'Token ยืนยันไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      await this.makePublicRequest<void>('/user/verify-email', {
        method: 'POST',
        body: { token: verificationToken.trim() }
      });
    });
  }

  /**
   * ✅ Resend verification email
   */
  async resendVerificationEmail(token: string): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>('/user/resend-verification', token, {
        method: 'POST'
      });
    });
  }

  /**
   * ✅ Delete user account
   */
  async deleteAccount(password: string, token: string): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    if (!password?.trim()) {
      return {
        success: false,
        error: 'กรุณากรอกรหัสผ่านเพื่อยืนยัน'
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>('/user/delete-account', token, {
        method: 'DELETE',
        body: { password: password.trim() }
      });
    });
  }

  /**
   * ✅ Get user statistics
   */
  async getUserStats(token: string): Promise<ApiResponse<any>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง'
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: any }>('/user/stats', token);
      return result.data;
    });
  }
}

export const userService = new UserService();
