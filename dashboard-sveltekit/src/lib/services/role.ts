import { BaseService } from './base';
import type { ApiResponse } from '$lib/types/common';

export interface TeamMember {
	_id: string;
	userId: string;
	userName: string;
	userEmail: string;
	role: 'owner' | 'admin' | 'editor' | 'viewer';
	joinedAt: string;
	lastActiveAt?: string;
}

export interface Invitation {
	_id: string;
	toEmail?: string;
	toUserId?: string;
	toUserName?: string;
	role: 'owner' | 'admin' | 'editor' | 'viewer';
	status: 'pending' | 'accepted' | 'rejected' | 'expired';
	message?: string;
	createdAt: string;
	expiresAt: string;
}

export interface CreateInvitationData {
	toEmail?: string;
	toUserId?: string;
	role: 'owner' | 'admin' | 'editor' | 'viewer';
	message?: string;
}

export interface UpdateRoleData {
	role: 'owner' | 'admin' | 'editor' | 'viewer';
}

export interface RoleListResponse {
	roles: TeamMember[];
	total: number;
}

export interface InvitationListResponse {
	invitations: Invitation[];
	total: number;
}

/**
 * ✅ Role Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class RoleService extends BaseService {

	/**
	 * ✅ Get team members/roles
	 */
	async getRoles(siteId: string, token: string): Promise<ApiResponse<RoleListResponse>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง'
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง'
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makeAuthenticatedRequest<{ data: RoleListResponse }>(
				`/role/${siteId}/roles`,
				token
			);
			return result.data;
		});
	}

	/**
	 * ✅ Get sent invitations
	 */
	async getSentInvitations(siteId: string, token: string): Promise<ApiResponse<InvitationListResponse>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง'
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง'
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makeAuthenticatedRequest<{ data: InvitationListResponse }>(
				`/role/${siteId}/invitations/sent`,
				token
			);
			return result.data;
		});
	}

	/**
	 * ✅ Create invitation
	 */
	async createInvitation(siteId: string, data: CreateInvitationData, token: string): Promise<ApiResponse<Invitation>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง'
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง'
			};
		} 

		console.log(data);

		return this.handleRequest(async () => {
			const result = await this.makeAuthenticatedRequest<{ data: Invitation }>(
				`/role/${siteId}/invitations`,
				token,
				{
					method: 'POST',
					body: data
				}
			);
			return result.data;
		});
	}

	/**
	 * ✅ Update role
	 */
	async updateRole(siteId: string, memberId: string, data: UpdateRoleData, token: string): Promise<ApiResponse<TeamMember>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง'
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง'
			};
		}

		if (!memberId?.trim()) {
			return {
				success: false,
				error: 'Member ID ไม่ถูกต้อง'
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makeAuthenticatedRequest<{ data: TeamMember }>(
				`/role/${siteId}/roles/${memberId}`,
				token,
				{
					method: 'PUT',
					body: data
				}
			);
			return result.data;
		});
	}

	/**
	 * ✅ Delete role (remove member)
	 */
	async deleteRole(siteId: string, memberId: string, token: string): Promise<ApiResponse<any>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง'
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง'
			};
		}

		if (!memberId?.trim()) {
			return {
				success: false,
				error: 'Member ID ไม่ถูกต้อง'
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makeAuthenticatedRequest<{ data: any }>(
				`/role/${siteId}/roles/${memberId}`,
				token,
				{
					method: 'DELETE'
				}
			);
			return result.data;
		});
	}

	/**
	 * ✅ Delete invitation (cancel invitation)
	 */
	async cancelInvitation(siteId: string, invitationId: string, token: string): Promise<ApiResponse<any>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง'
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง'
			};
		}

		if (!invitationId?.trim()) {
			return {
				success: false,
				error: 'Invitation ID ไม่ถูกต้อง'
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makeAuthenticatedRequest<{ data: any }>(
				`/role/${siteId}/invitations/${invitationId}`,
				token,
				{
					method: 'DELETE'
				}
			);
			return result.data;
		});
	}

	/**
	 * ✅ Resend invitation
	 */
	async resendInvitation(siteId: string, invitationId: string, token: string): Promise<ApiResponse<Invitation>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง'
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง'
			};
		}

		if (!invitationId?.trim()) {
			return {
				success: false,
				error: 'Invitation ID ไม่ถูกต้อง'
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makeAuthenticatedRequest<{ data: Invitation }>(
				`/role/${siteId}/invitations/${invitationId}/resend`,
				token,
				{
					method: 'POST'
				}
			);
			return result.data;
		});
	}
}

export const roleService = new RoleService();
