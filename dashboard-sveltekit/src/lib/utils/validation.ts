import { type ZodSchema } from 'zod';

export interface ValidationResult<T> {
	success: boolean;
	data?: T;
	errors?: Record<string, string>;
}

export function validateForm<T>(
	schema: ZodSchema<T>,
	data: unknown
): ValidationResult<T> {
	const result = schema.safeParse(data);
	if (result.success) {
		return {
			success: true,
			data: result.data
		};
	}

	// Convert ZodError to Record<string, string>
	const errors: Record<string, string> = {};

	if (result.error) {
		result.error.issues.forEach((issue) => {
			const path = issue.path.join('.');
			if (path) {
				errors[path] = issue.message;
			}
		});
	}

	return {
		success: false,
		errors
	};
}

export function getFieldError(
	errors: Record<string, string> | undefined,
	fieldName: string
): string | undefined {
	return errors?.[fieldName];
}

export function hasFieldError(
	errors: Record<string, string> | undefined,
	fieldName: string
): boolean {
	return !!getFieldError(errors, fieldName);
}

export function clearFieldError(
	errors: Record<string, string>,
	fieldName: string
): Record<string, string> {
	const newErrors = { ...errors };
	delete newErrors[fieldName];
	return newErrors;
}

export function clearAllErrors(): Record<string, string> {
	return {};
} 