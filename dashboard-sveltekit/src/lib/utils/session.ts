import crypto from 'crypto';
import type { RequestEvent } from '@sveltejs/kit';

/**
 * ✅ Session Security Utilities
 */

export interface SessionData {
    id: string;
    userId?: string;
    createdAt: number;
    lastActivity: number;
    ipAddress: string;
    userAgent: string;
}

// In-memory session store (ใน production ควรใช้ Redis หรือ Database)
const sessionStore = new Map<string, SessionData>();

export function createSession(userId: string, ipAddress: string, userAgent: string): string {
    const sessionId = crypto.randomUUID();
    const now = Date.now();

    const sessionData: SessionData = {
        id: sessionId,
        userId,
        createdAt: now,
        lastActivity: now,
        ipAddress,
        userAgent
    };

    sessionStore.set(sessionId, sessionData);
    return sessionId;
}

export function getSession(sessionId: string): SessionData | null {
    return sessionStore.get(sessionId) || null;
}

export function updateSessionActivity(sessionId: string): boolean {
    const session = sessionStore.get(sessionId);
    if (session) {
        session.lastActivity = Date.now();
        return true;
    }
    return false;
}

export function validateSession(
    sessionId: string,
    ipAddress: string,
    userAgent: string
): SessionData | null {
    const session = getSession(sessionId);

    if (!session) {
        return null;
    }

    // Check session timeout (30 minutes)
    const SESSION_TIMEOUT = 30 * 60 * 1000;
    if (Date.now() - session.lastActivity > SESSION_TIMEOUT) {
        destroySession(sessionId);
        return null;
    }

    // Validate IP and User Agent for session hijacking protection
    if (session.ipAddress !== ipAddress || session.userAgent !== userAgent) {
        console.warn('Session validation failed: IP or User Agent mismatch', {
            sessionId,
            expectedIP: session.ipAddress,
            actualIP: ipAddress,
            expectedUA: session.userAgent.substring(0, 50),
            actualUA: userAgent.substring(0, 50)
        });
        destroySession(sessionId);
        return null;
    }

    // Update last activity
    updateSessionActivity(sessionId);
    return session;
}

export function destroySession(sessionId: string): void {
    sessionStore.delete(sessionId);
}

export function rotateSession(oldSessionId: string, userId: string, ipAddress: string, userAgent: string): string {
    // Destroy old session
    destroySession(oldSessionId);

    // Create new session
    return createSession(userId, ipAddress, userAgent);
}

export function cleanupExpiredSessions(): void {
    const now = Date.now();
    const SESSION_TIMEOUT = 30 * 60 * 1000;

    for (const [sessionId, session] of sessionStore.entries()) {
        if (now - session.lastActivity > SESSION_TIMEOUT) {
            sessionStore.delete(sessionId);
        }
    }
}

// Cleanup expired sessions every 5 minutes
setInterval(cleanupExpiredSessions, 5 * 60 * 1000);