import type { RequestEvent } from '@sveltejs/kit';
import { checkRateLimit } from './rate-limit';
import { validateCSRFToken } from './csrf';
import { logger, LogCategory } from './logger';

/**
 * ✅ Security Middleware for Protected Routes
 */

export interface SecurityCheckResult {
    allowed: boolean;
    error?: string;
    statusCode?: number;
}

/**
 * Check CSRF token for state-changing operations
 */
export function checkCSRFProtection(event: RequestEvent): SecurityCheckResult {
    // Skip CSRF for GET requests
    if (event.request.method === 'GET') {
        return { allowed: true };
    }

    try {
        const isValid = validateCSRFToken(event);

        if (!isValid) {
            logger.warn(LogCategory.SYSTEM, 'csrf_validation_failed', 'CSRF token validation failed', {
                method: event.request.method,
                path: event.url.pathname,
                hasToken: !!event.cookies.get('csrf_token')
            });

            return {
                allowed: false,
                error: 'CSRF token validation failed',
                statusCode: 403
            };
        }

        return { allowed: true };
    } catch (error) {
        logger.error(LogCategory.SYSTEM, 'csrf_check_error', 'Error checking CSRF token', {
            error: error instanceof Error ? error.message : 'Unknown error',
            method: event.request.method,
            path: event.url.pathname
        });

        return {
            allowed: false,
            error: 'Security validation failed',
            statusCode: 500
        };
    }
}

/**
 * Check rate limiting for sensitive operations
 */
export function checkSensitiveOperationRateLimit(
    event: RequestEvent,
    identifier: string
): SecurityCheckResult {
    const rateLimitResult = checkRateLimit('API_SENSITIVE', identifier);

    if (!rateLimitResult.allowed) {
        logger.warn(LogCategory.SYSTEM, 'sensitive_operation_rate_limited', 'Sensitive operation rate limited', {
            path: event.url.pathname,
            identifier: identifier.substring(0, 8) + '***',
            remaining: rateLimitResult.remaining,
            blocked: rateLimitResult.blocked
        });

        const errorMessage = rateLimitResult.blocked
            ? 'บัญชีถูกล็อกชั่วคราว เนื่องจากมีการใช้งานผิดปกติ'
            : 'การใช้งานเกินขีดจำกัด กรุณารอสักครู่';

        return {
            allowed: false,
            error: errorMessage,
            statusCode: 429
        };
    }

    return { allowed: true };
}

/**
 * Comprehensive security check for protected routes
 */
export function performSecurityChecks(
    event: RequestEvent,
    options: {
        requireCSRF?: boolean;
        checkSensitiveRateLimit?: boolean;
        identifier?: string;
    } = {}
): SecurityCheckResult {
    // Check CSRF if required
    if (options.requireCSRF) {
        const csrfResult = checkCSRFProtection(event);
        if (!csrfResult.allowed) {
            return csrfResult;
        }
    }

    // Check sensitive operation rate limit if required
    if (options.checkSensitiveRateLimit && options.identifier) {
        const rateLimitResult = checkSensitiveOperationRateLimit(event, options.identifier);
        if (!rateLimitResult.allowed) {
            return rateLimitResult;
        }
    }

    return { allowed: true };
}