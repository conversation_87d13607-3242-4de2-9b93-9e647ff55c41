import { z } from 'zod';

// Custom validation functions (used ones only)
const validateEmail = (value: string) => {
	if (!value) return 'กรุณากรอกอีเมล';
	if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'รูปแบบอีเมลไม่ถูกต้อง';
	return undefined;
};

const validatePassword = (value: string) => {
	if (!value) return 'กรุณากรอกรหัสผ่าน';
	if (value.length < 8) return 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร';
	if (value.length > 128) return 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร';
	if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
		return 'รหัสผ่านต้องมีตัวพิมพ์เล็ก ตัวพิมพ์ใหญ่ และตัวเลข';
	}
	return undefined;
};

const validateName = (value: string) => {
	if (!value) return 'กรุณากรอกชื่อ';
	if (value.length < 2) return 'ชื่อต้องมีอย่างน้อย 2 ตัวอักษร';
	if (value.length > 50) return 'ชื่อต้องไม่เกิน 50 ตัวอักษร';
	return undefined;
};

const validateSiteName = (value: string) => {
	if (!value) return 'กรุณากรอกชื่อเว็บไซต์';
	if (value.length < 2) return 'ชื่อเว็บไซต์ต้องมีอย่างน้อย 2 ตัวอักษร';
	if (value.length > 50) return 'ชื่อเว็บไซต์ต้องไม่เกิน 50 ตัวอักษร';
	return undefined;
};

// Basic domain validation functions (for client-side UX only)
const validateSubdomain = (value: string) => {
	if (!value) return 'กรุณากรอกซับโดเมน';
	if (value.length < 3) return 'ซับโดเมนต้องมีอย่างน้อย 3 ตัวอักษร';
	if (value.length > 20) return 'ซับโดเมนต้องไม่เกิน 20 ตัวอักษร';
	if (!/^[a-z0-9-]+$/.test(value)) {
		return 'ซับโดเมนต้องเป็นตัวอักษรภาษาอังกฤษ ตัวเลข และขีดเท่านั้น';
	}
	return undefined;
};

const validateCustomDomain = (value: string) => {
	if (!value) return 'กรุณากรอกโดเมน';
	if (value.length < 1) return 'โดเมนต้องมีอย่างน้อย 1 ตัวอักษร';
	if (value.length > 100) return 'โดเมนต้องไม่เกิน 100 ตัวอักษร';
	if (!/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/.test(value)) {
		return 'รูปแบบโดเมนไม่ถูกต้อง';
	}
	return undefined;
};

const validatePhone = (value: string) => {
	if (!value) return undefined; // optional
	if (!/^[0-9+\-\s()]*$/.test(value)) return 'หมายเลขโทรศัพท์ไม่ถูกต้อง';
	if (value.length > 20) return 'หมายเลขโทรศัพท์ต้องไม่เกิน 20 ตัวอักษร';
	return undefined;
};

const validateUrl = (value: string) => {
	if (!value) return undefined; // optional
	if (!/^https?:\/\/.+/.test(value)) return 'URL ไม่ถูกต้อง';
	return undefined;
};

const validateFileSize = (value: number) => {
	if (value > 10 * 1024 * 1024) return 'ไฟล์ต้องมีขนาดไม่เกิน 10MB';
	return undefined;
};

const validateFileType = (value: string) => {
	if (!/^(image\/|application\/pdf|text\/)/.test(value)) {
		return 'ไฟล์ต้องเป็นรูปภาพ, PDF หรือข้อความเท่านั้น';
	}
	return undefined;
};

const validatePostalCode = (value: string) => {
	if (!/^[0-9]{5}$/.test(value)) return 'รหัสไปรษณีย์ต้องเป็นตัวเลข 5 หลัก';
	return undefined;
};

const validateEnum = (value: string, allowedValues: string[], fieldName: string) => {
	if (!allowedValues.includes(value)) {
		return `${fieldName} ต้องเป็น ${allowedValues.join(', ')}`;
	}
	return undefined;
};

// Simple schemas (keep only used ones)
export const emailSchema = z.string();
export const passwordSchema = z.string();
export const nameSchema = z.string();
export const phoneSchema = z.string().optional();
export const urlSchema = z.string().optional();

// Search and filter schemas
export const searchSchema = z.object({
	query: z.string().optional(),
	page: z.number().optional(),
	limit: z.number().optional(),
	sortBy: z.string().optional(),
	sortOrder: z.string().optional()
});

// Pagination schema
export const paginationSchema = z.object({
	page: z.number().optional(),
	limit: z.number().optional(),
	total: z.number().optional()
});

// File upload schema
export const fileUploadSchema = z.object({
	filename: z.string(),
	size: z.number(),
	type: z.string()
});

// Address schema
export const addressSchema = z.object({
	street: z.string(),
	city: z.string(),
	state: z.string(),
	postalCode: z.string(),
	country: z.string()
});

// Social media schema
export const socialMediaSchema = z.object({
	facebook: z.string().optional(),
	twitter: z.string().optional(),
	instagram: z.string().optional(),
	linkedin: z.string().optional(),
	youtube: z.string().optional()
});

// Settings schema
export const settingsSchema = z.object({
	notifications: z.object({
		email: z.boolean(),
		push: z.boolean(),
		sms: z.boolean()
	}),
	privacy: z.object({
		profileVisibility: z.string(),
		showEmail: z.boolean(),
		showPhone: z.boolean()
	}),
	theme: z.string()
});

// Validation functions
export function validateSearchForm(data: any) {
	const errors: Record<string, string> = {};

	if (data.query && data.query.length > 100) {
		errors.query = 'คำค้นหาต้องไม่เกิน 100 ตัวอักษร';
	}

	return {
		success: Object.keys(errors).length === 0,
		errors
	};
}

export function validateFileUploadForm(data: any) {
	const errors: Record<string, string> = {};

	if (!data.filename) {
		errors.filename = 'กรุณาเลือกไฟล์';
	} else if (data.filename.length > 255) {
		errors.filename = 'ชื่อไฟล์ต้องไม่เกิน 255 ตัวอักษร';
	}

	const sizeError = validateFileSize(data.size);
	if (sizeError) errors.size = sizeError;

	const typeError = validateFileType(data.type);
	if (typeError) errors.type = typeError;

	return {
		success: Object.keys(errors).length === 0,
		errors
	};
}

export function validateAddressForm(data: any) {
	const errors: Record<string, string> = {};

	if (!data.street) {
		errors.street = 'กรุณากรอกที่อยู่';
	} else if (data.street.length > 200) {
		errors.street = 'ที่อยู่ต้องไม่เกิน 200 ตัวอักษร';
	}

	if (!data.city) {
		errors.city = 'กรุณากรอกเมือง';
	} else if (data.city.length > 100) {
		errors.city = 'เมืองต้องไม่เกิน 100 ตัวอักษร';
	}

	if (!data.state) {
		errors.state = 'กรุณากรอกจังหวัด';
	} else if (data.state.length > 100) {
		errors.state = 'จังหวัดต้องไม่เกิน 100 ตัวอักษร';
	}

	const postalError = validatePostalCode(data.postalCode);
	if (postalError) errors.postalCode = postalError;

	if (!data.country) {
		errors.country = 'กรุณาเลือกประเทศ';
	} else if (data.country.length > 100) {
		errors.country = 'ประเทศต้องไม่เกิน 100 ตัวอักษร';
	}

	return {
		success: Object.keys(errors).length === 0,
		errors
	};
}

export function validateSocialMediaForm(data: any) {
	const errors: Record<string, string> = {};

	const fields = ['facebook', 'twitter', 'instagram', 'linkedin', 'youtube'];
	fields.forEach(field => {
		if (data[field]) {
			const urlError = validateUrl(data[field]);
			if (urlError) errors[field] = urlError;
		}
	});

	return {
		success: Object.keys(errors).length === 0,
		errors
	};
}

export function validateSettingsForm(data: any) {
	const errors: Record<string, string> = {};

	const visibilityError = validateEnum(
		data.privacy?.profileVisibility,
		['public', 'private', 'friends'],
		'Profile Visibility'
	);
	if (visibilityError) errors.profileVisibility = visibilityError;

	const themeError = validateEnum(
		data.theme,
		['light', 'dark', 'auto'],
		'Theme'
	);
	if (themeError) errors.theme = themeError;

	return {
		success: Object.keys(errors).length === 0,
		errors
	};
}

// Simplified client-side validation (basic checks only)
export function validateCreateSiteForm(data: any) {
	const errors: Record<string, string> = {};

	// Basic site name validation
	const siteNameError = validateSiteName(data.siteName);
	if (siteNameError) errors.siteName = siteNameError;

	// Basic domain type validation
	if (!data.typeDomain || !['subdomain', 'custom'].includes(data.typeDomain)) {
		errors.typeDomain = 'กรุณาเลือกประเภทโดเมน';
	}

	// Basic domain validation (detailed validation will be done on server)
	if (data.typeDomain === 'subdomain') {
		if (!data.subDomain) errors.subDomain = 'กรุณากรอกซับโดเมน';
		if (!data.mainDomain) errors.mainDomain = 'กรุณาเลือกโดเมนหลัก';
	} else if (data.typeDomain === 'custom') {
		if (!data.customDomain) errors.customDomain = 'กรุณากรอกโดเมน';
	}

	// Basic package validation
	if (!data.packageType) {
		errors.packageType = 'กรุณาเลือกแพ็คเกจ';
	}

	return {
		success: Object.keys(errors).length === 0,
		errors
	};
}

// Simplified client-side domain validation
export function validateCheckDomainForm(data: any) {
	const errors: Record<string, string> = {};

	// Basic domain type validation
	if (!data.typeDomain || !['subdomain', 'custom'].includes(data.typeDomain)) {
		errors.typeDomain = 'กรุณาเลือกประเภทโดเมน';
	}

	// Basic field presence validation (detailed validation on server)
	if (data.typeDomain === 'subdomain') {
		if (!data.subDomain) errors.subDomain = 'กรุณากรอกซับโดเมน';
		if (!data.mainDomain) errors.mainDomain = 'กรุณาเลือกโดเมนหลัก';
	} else if (data.typeDomain === 'custom') {
		if (!data.customDomain) errors.customDomain = 'กรุณากรอกโดเมน';
	}

	return {
		success: Object.keys(errors).length === 0,
		errors
	};
}

// Types
export interface SearchForm {
	query?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortOrder?: string;
}

export interface PaginationForm {
	page?: number;
	limit?: number;
	total?: number;
}

export interface FileUploadForm {
	filename: string;
	size: number;
	type: string;
}

export interface AddressForm {
	street: string;
	city: string;
	state: string;
	postalCode: string;
	country: string;
}

export interface SocialMediaForm {
	facebook?: string;
	twitter?: string;
	instagram?: string;
	linkedin?: string;
	youtube?: string;
}

export interface SettingsForm {
	notifications: {
		email: boolean;
		push: boolean;
		sms: boolean;
	};
	privacy: {
		profileVisibility: string;
		showEmail: boolean;
		showPhone: boolean;
	};
	theme: string;
}

export interface CreateSiteForm {
	siteName: string;
	typeDomain: 'subdomain' | 'custom';
	subDomain?: string;
	mainDomain?: string;
	customDomain?: string;
	packageType: string;
}

export interface CheckDomainForm {
	typeDomain: 'subdomain' | 'custom';
	subDomain?: string;
	mainDomain?: string;
	customDomain?: string;
} 