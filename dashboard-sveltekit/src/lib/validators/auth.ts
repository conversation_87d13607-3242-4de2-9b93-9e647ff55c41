import { z } from 'zod';



// Login Schema
export const loginSchema = z.object({
	email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง'),
	password: z.string().min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร').max(128, 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร'),
	rememberMe: z.boolean()
});

// Register Schema
export const registerSchema = z.object({
	email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง'),
	password: z.string()
		.min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร')
		.max(128, 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร')
		.regex(/^[a-zA-Z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?`~]+$/, 'รหัสผ่านต้องใช้เฉพาะภาษาอังกฤษ ตัวเลข และอักขระพิเศษเท่านั้น'),
	confirmPassword: z.string(),
	agreeToTerms: z.boolean().refine(val => val === true, 'กรุณายอมรับเงื่อนไขการใช้งาน')
}).refine(data => data.password === data.confirmPassword, {
	message: 'รหัสผ่านไม่ตรงกัน',
	path: ['confirmPassword']
});

// Password Reset Schema
export const passwordResetSchema = z.object({
	email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง')
});

// Change Password Schema
export const changePasswordSchema = z.object({
	currentPassword: z.string().min(1, 'กรุณากรอกรหัสผ่านปัจจุบัน'),
	newPassword: z.string()
		.min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร')
		.max(128, 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร')
		.regex(/^[a-zA-Z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?`~]+$/, 'รหัสผ่านต้องใช้เฉพาะภาษาอังกฤษ ตัวเลข และอักขระพิเศษเท่านั้น'),
	confirmNewPassword: z.string()
}).refine(data => data.newPassword === data.confirmNewPassword, {
	message: 'รหัสผ่านไม่ตรงกัน',
	path: ['confirmNewPassword']
});

// Profile Update Schema
export const profileUpdateSchema = z.object({
	firstName: z.string().min(1, 'กรุณากรอกชื่อ').max(50, 'ชื่อต้องไม่เกิน 50 ตัวอักษร'),
	lastName: z.string().min(1, 'กรุณากรอกนามสกุล').max(50, 'นามสกุลต้องไม่เกิน 50 ตัวอักษร'),
	email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง'),
	phone: z.string().regex(/^[0-9+\-\s()]*$/, 'หมายเลขโทรศัพท์ไม่ถูกต้อง').max(20, 'หมายเลขโทรศัพท์ต้องไม่เกิน 20 ตัวอักษร').optional(),
	bio: z.string().max(500, 'ข้อมูลส่วนตัวต้องไม่เกิน 500 ตัวอักษร').optional()
});

// Validation functions using zod
export function validateLoginForm(data: unknown) {
	const result = loginSchema.safeParse(data);
	if (result.success) {
		return {
			success: true,
			data: result.data,
			errors: {}
		};
	}

	const errors: Record<string, string> = {};
	result.error.issues.forEach((issue) => {
		const path = issue.path.join('.');
		if (path) {
			errors[path] = issue.message;
		}
	});

	return {
		success: false,
		errors
	};
}

export function validateRegisterForm(data: unknown) {
	const result = registerSchema.safeParse(data);
	if (result.success) {
		return {
			success: true,
			data: result.data,
			errors: {}
		};
	}

	const errors: Record<string, string> = {};
	result.error.issues.forEach((issue) => {
		const path = issue.path.join('.');
		if (path) {
			errors[path] = issue.message;
		}
	});

	return {
		success: false,
		errors
	};
}

// Types
export interface LoginForm {
	email: string;
	password: string;
	rememberMe: boolean;
}

export interface RegisterForm {
	email: string;
	password: string;
	confirmPassword: string;
	agreeToTerms: boolean;
}

export interface PasswordResetForm {
	email: string;
}

export interface ChangePasswordForm {
	currentPassword: string;
	newPassword: string;
	confirmNewPassword: string;
}

export interface ProfileUpdateForm {
	firstName: string;
	lastName: string;
	email: string;
	phone?: string;
	bio?: string;
} 