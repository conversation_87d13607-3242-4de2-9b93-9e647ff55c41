/* Page Transition Animations - Fade Effects */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes fadeInSlow {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeOutSlow {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeOutDown {
    from {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }

    to {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate3d(0, -30px, 0);
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeOutUp {
    from {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }

    to {
        opacity: 0;
        transform: translate3d(0, -30px, 0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translate3d(-30px, 0, 0);
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translate3d(30px, 0, 0);
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale3d(0.8, 0.8, 1);
    }

    to {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }
}

@keyframes scaleOut {
    from {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }

    to {
        opacity: 0;
        transform: scale3d(0.8, 0.8, 1);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 1);
    }

    50% {
        opacity: 1;
    }

    to {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }

    50% {
        opacity: 1;
        transform: scale3d(0.3, 0.3, 1);
    }

    to {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 1);
    }
}

/* Loading Animations */
@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }

    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes bounce {

    0%,
    20%,
    53%,
    80%,
    100% {
        transform: translate3d(0, 0, 0);
    }

    40%,
    43% {
        transform: translate3d(0, -30px, 0);
    }

    70% {
        transform: translate3d(0, -15px, 0);
    }

    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Notification Animations - Fade Effects */
@keyframes fadeInFromTop {
    from {
        opacity: 0;
        transform: translate3d(0, -20px, 0);
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeOutToTop {
    from {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }

    to {
        opacity: 0;
        transform: translate3d(0, -20px, 0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translate3d(-100%, 0, 0);
        visibility: visible;
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideOutLeft {
    from {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }

    to {
        opacity: 0;
        transform: translate3d(-100%, 0, 0);
        visibility: hidden;
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }

    to {
        opacity: 0;
        transform: translate3d(100%, 0, 0);
        visibility: hidden;
    }
}

@keyframes wiggle {

    0%,
    7% {
        transform: rotateZ(0);
    }

    15% {
        transform: rotateZ(-15deg);
    }

    20% {
        transform: rotateZ(10deg);
    }

    25% {
        transform: rotateZ(-10deg);
    }

    30% {
        transform: rotateZ(6deg);
    }

    35% {
        transform: rotateZ(-4deg);
    }

    40%,
    100% {
        transform: rotateZ(0);
    }
}

/* Utility Classes - Enhanced Effects */
.animate-fade-in {
    animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
}

.animate-fade-out {
    animation: fadeOut 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
}

.animate-fade-in-slow {
    animation: fadeInSlow 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
}

.animate-fade-out-slow {
    animation: fadeOutSlow 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
}

.animate-fade-in-up {
    animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-fade-out-down {
    animation: fadeOutDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-fade-in-down {
    animation: fadeInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-fade-out-up {
    animation: fadeOutUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-fade-in-right {
    animation: fadeInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-scale-in {
    animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-scale-out {
    animation: scaleOut 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-zoom-in {
    animation: zoomIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-zoom-out {
    animation: zoomOut 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-slide-in-left {
    animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-slide-in-right {
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-slide-out-left {
    animation: slideOutLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-slide-out-right {
    animation: slideOutRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
    animation: shimmer 2s infinite;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent);
    background-size: 200px 100%;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-fade-in-top {
    animation: fadeInFromTop 0.4s ease-out;
}

.animate-fade-out-top {
    animation: fadeOutToTop 0.3s ease-in;
}

.animate-wiggle {
    animation: wiggle 0.5s ease-in-out;
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.hover-scale {
    transition: transform 0.2s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow 0.2s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Smooth Transitions */
.transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Loading Skeleton */
.skeleton {
    background: linear-gradient(90deg,
            hsl(var(--b2)) 25%,
            hsl(var(--b3)) 50%,
            hsl(var(--b2)) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

/* Page Transition Container - Enhanced */
.page-transition {
    position: relative;
    overflow: hidden;
    contain: layout style paint;
    will-change: transform, opacity;
}

.page-transition-enter {
    animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-leave {
    animation: fadeOutDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Advanced Page Transitions */
.page-transition-slide-left {
    animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-slide-right {
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-zoom {
    animation: zoomIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-scale {
    animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Notification Bell Animation */
.notification-bell {
    transition: transform 0.2s ease;
}

.notification-bell:hover {
    transform: scale(1.1);
}

.notification-bell.has-notifications {
    animation: wiggle 0.5s ease-in-out;
}

/* Stagger Animation Delays */
.stagger-1 {
    animation-delay: 0.1s;
}

.stagger-2 {
    animation-delay: 0.2s;
}

.stagger-3 {
    animation-delay: 0.3s;
}

.stagger-4 {
    animation-delay: 0.4s;
}

.stagger-5 {
    animation-delay: 0.5s;
}

/* Performance Optimizations */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

.smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Enhanced Hover Effects */
.hover-lift-smooth {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, box-shadow;
}

.hover-lift-smooth:hover {
    transform: translateY(-4px) translateZ(0);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hover-scale-smooth {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
}

.hover-scale-smooth:hover {
    transform: scale(1.05) translateZ(0);
}

.hover-glow-smooth {
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: box-shadow;
}

.hover-glow-smooth:hover {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
}

/* Focus States */
.focus-ring-smooth {
    transition: box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.focus-ring-smooth:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg,
            hsl(var(--b2)) 25%,
            hsl(var(--b3)) 50%,
            hsl(var(--b2)) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
    border-radius: 0.375rem;
}

.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive Animations */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .gpu-accelerated {
        will-change: auto;
    }

    .hover-lift-smooth,
    .hover-scale-smooth,
    .hover-glow-smooth,
    .focus-ring-smooth {
        transition: none;
    }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
    .hover-lift-smooth:hover {
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .hover-glow-smooth:hover {
        box-shadow: 0 0 30px rgba(96, 165, 250, 0.4);
    }
}