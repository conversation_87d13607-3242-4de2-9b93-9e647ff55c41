// ✅ Auth Schema - Zod Validation
import { z } from 'zod';
import type { User } from '$lib/types/user';

// ✅ Zod Schemas
export const signinSchema = z.object({
    email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง'),
    password: z.string().min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร').max(128, 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร')
});

export const signupSchema = z.object({
    email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง'),
    password: z.string()
        .min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร')
        .max(128, 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร'),
    firstName: z.string().min(1, 'กรุณากรอกชื่อ').max(50, 'ชื่อต้องไม่เกิน 50 ตัวอักษร').optional(),
    lastName: z.string().min(1, 'กรุณากรอกนามสกุล').max(50, 'นามสกุลต้องไม่เกิน 50 ตัวอักษร').optional(),
    confirmPassword: z.string().optional()
}).refine(data => !data.confirmPassword || data.password === data.confirmPassword, {
    message: 'รหัสผ่านไม่ตรงกัน',
    path: ['confirmPassword']
});

export const forgotPasswordSchema = z.object({
    email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง')
});

export const resetPasswordSchema = z.object({
    token: z.string().min(1, 'Token ไม่ถูกต้อง'),
    password: z.string().min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร').max(128, 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร'),
    confirmPassword: z.string().optional()
}).refine(data => !data.confirmPassword || data.password === data.confirmPassword, {
    message: 'รหัสผ่านไม่ตรงกัน',
    path: ['confirmPassword']
});

export const verifyEmailSchema = z.object({
    token: z.string().min(1, 'Token ไม่ถูกต้อง')
});

// ✅ Type inference from schemas
export type SigninData = z.infer<typeof signinSchema>;
export type SignupData = z.infer<typeof signupSchema>;
export type ForgotPasswordData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordData = z.infer<typeof resetPasswordSchema>;
export type VerifyEmailData = z.infer<typeof verifyEmailSchema>;

// Auth Response Types
export interface AuthResponse {
    success: boolean;
    data?: {
        user: User;
        token: string;
        refreshToken: string;
        sessionId?: string;
    };
    error?: string;
    message?: string;
}

export interface RefreshTokenResponse {
    success: boolean;
    data?: {
        token: string;
        refreshToken: string;
        user: User;
    };
    error?: string;
}

// ✅ Validation Functions using Zod
export function validateSigninData(data: unknown): { success: boolean; data?: SigninData; error?: string } {
    const result = signinSchema.safeParse(data);
    if (result.success) {
        return { success: true, data: result.data };
    }

    const firstError = result.error.issues[0];
    return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateSignupData(data: unknown): { success: boolean; data?: SignupData; error?: string } {
    const result = signupSchema.safeParse(data);
    if (result.success) {
        return { success: true, data: result.data };
    }

    const firstError = result.error.issues[0];
    return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateForgotPasswordData(data: unknown): { success: boolean; data?: ForgotPasswordData; error?: string } {
    const result = forgotPasswordSchema.safeParse(data);
    if (result.success) {
        return { success: true, data: result.data };
    }

    const firstError = result.error.issues[0];
    return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateResetPasswordData(data: unknown): { success: boolean; data?: ResetPasswordData; error?: string } {
    const result = resetPasswordSchema.safeParse(data);
    if (result.success) {
        return { success: true, data: result.data };
    }

    const firstError = result.error.issues[0];
    return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

// ✅ Helper Functions
export function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

export function sanitizeAuthData<T extends Record<string, unknown>>(data: T): T {
    const sanitized = { ...data } as T;

    // Trim string fields
    Object.keys(sanitized).forEach(key => {
        if (typeof sanitized[key] === 'string') {
            (sanitized as Record<string, unknown>)[key] = (sanitized[key] as string).trim();
        }
    });

    return sanitized;
}