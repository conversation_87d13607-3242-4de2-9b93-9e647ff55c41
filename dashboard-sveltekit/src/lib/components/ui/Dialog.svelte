<script lang="ts">
	import { fade, scale } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import Icon from '@iconify/svelte';

	interface Props {
		open?: boolean;
		title?: string;
		iconTitle?: string;
		description?: string;
		size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | 'full';
		position?: 'top' | 'center' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
		variant?: 'default' | 'bordered' | 'ghost' | 'filled';
		color?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'info' | 'neutral';
		backdrop?: boolean;
		backdropBlur?: boolean;
		closeOnBackdrop?: boolean;
		closeOnEscape?: boolean;
		showCloseButton?: boolean;
		closeButtonIcon?: string;
		className?: string;
		children?: any;
		onClose?: () => void;
		onOpen?: () => void;
	}

	let {
		open = false,
		title = '',
		iconTitle = '',
		description = '',
		size = 'md',
		position = 'center',
		variant = 'default',
		color = 'primary',
		backdrop = true,
		backdropBlur = true,
		closeOnBackdrop = true,
		closeOnEscape = true,
		showCloseButton = true,
		closeButtonIcon = 'mdi:close',
		className = '',
		children,
		onClose,
		onOpen
	}: Props = $props();

	// Effects
	$effect(() => {
		if (open) {
			onOpen?.();
			document.body.style.overflow = 'hidden';
		} else {
			document.body.style.overflow = '';
		}
	});

	// Computed
	const sizeClasses = $derived({
		'xs': 'max-w-xs',
		'sm': 'max-w-sm',
		'md': 'max-w-md',
		'lg': 'max-w-lg',
		'xl': 'max-w-xl',
		'2xl': 'max-w-2xl',
		'3xl': 'max-w-3xl',
		'4xl': 'max-w-4xl',
		'5xl': 'max-w-5xl',
		'6xl': 'max-w-6xl',
		'7xl': 'max-w-7xl',
		'full': 'max-w-full'
	});

	const positionClasses = $derived({
		'top': 'items-start justify-center pt-4',
		'center': 'items-center justify-center',
		'bottom': 'items-end justify-center pb-4',
		'left': 'items-center justify-start pl-4',
		'right': 'items-center justify-end pr-4',
		'top-left': 'items-start justify-start pt-4 pl-4',
		'top-right': 'items-start justify-end pt-4 pr-4',
		'bottom-left': 'items-end justify-start pb-4 pl-4',
		'bottom-right': 'items-end justify-end pb-4 pr-4'
	});

	const variantClasses = $derived({
		'default': 'bg-base-100 border border-base-300',
		'bordered': 'bg-base-100 border-2 border-base-300',
		'ghost': 'bg-base-100/80 backdrop-blur',
		'filled': 'bg-base-200 border border-base-300'
	});

	const colorClasses = $derived({
		'primary': 'text-primary',
		'secondary': 'text-secondary',
		'accent': 'text-accent',
		'success': 'text-success',
		'warning': 'text-warning',
		'error': 'text-error',
		'info': 'text-info',
		'neutral': 'text-neutral'
	});

	const backdropClasses = $derived(
		`fixed inset-0 z-50 flex ${positionClasses[position]} ${
			backdrop ? 'bg-black/50' : ''
		} ${backdropBlur ? 'backdrop-blur-sm' : ''}`
	);

	const dialogClasses = $derived(
		`relative max-h-[90vh] overflow-auto rounded-lg shadow-xl p-6 ${
			sizeClasses[size]
		} ${variantClasses[variant]} ${colorClasses[color]} ${className}`
	);

	// Functions
	function handleClose() {
		onClose?.();
	}

	function handleBackdropClick(event: MouseEvent) {
		if (closeOnBackdrop && event.target === event.currentTarget) {
			handleClose();
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (closeOnEscape && event.key === 'Escape') {
			handleClose();
		}
	}

	// Keyboard event listener
	$effect(() => {
		if (open) {
			document.addEventListener('keydown', handleKeydown);
			return () => document.removeEventListener('keydown', handleKeydown);
		}
	});
</script>

{#if open}
	<div
		class={backdropClasses}
		onclick={handleBackdropClick}
		role="dialog"
		aria-modal="true"
		tabindex="-1"
		transition:fade={{ duration: 200 }}
	>
		<div
			class={dialogClasses}
			transition:scale={{ duration: 200, easing: quintOut }}
		>
			{#if title || showCloseButton}
				<div class="flex items-center justify-between mb-4">
					{#if title}
						<div class="flex items-center gap-2">
							{#if iconTitle}
								<Icon icon={iconTitle} class="w-5 h-5" />
							{/if}
							<h2 class="text-lg font-semibold">{title}</h2>
						</div>
					{/if}
					{#if showCloseButton}
						<button
							type="button"
							class="btn btn-ghost btn-sm btn-circle"
							onclick={handleClose}
							aria-label="ปิด"
						>
							<Icon icon={closeButtonIcon} class="w-4 h-4" />
						</button>
					{/if}
				</div>
			{/if}

			{#if description}
				<p class="text-base-content/70 mb-4">{description}</p>
			{/if}

			<div class="dialog-content">
				{@render children()}
			</div>
		</div>
	</div>
{/if}

<style>
	.dialog-content {
		width: 100%;
	}
	
	/* Custom scrollbar for dialog content */
	.dialog-content::-webkit-scrollbar {
		width: 6px;
	}
	
	.dialog-content::-webkit-scrollbar-track {
		background: transparent;
	}
	
	.dialog-content::-webkit-scrollbar-thumb {
		background: hsl(var(--bc) / 0.3);
		border-radius: 3px;
	}
	
	.dialog-content::-webkit-scrollbar-thumb:hover {
		background: hsl(var(--bc) / 0.5);
	}
</style> 