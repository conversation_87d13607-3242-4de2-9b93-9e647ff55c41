<script lang="ts">
  import { onMount } from 'svelte';

  interface Props {
    siteKey: string;
    version?: 'v2' | 'v3';
    theme?: 'light' | 'dark';
    size?: 'normal' | 'compact' | 'invisible';
    action?: string;
    badge?: 'bottomright' | 'bottomleft' | 'inline';
    language?: string;
    tabindex?: number;
    callback?: (token: string) => void;
    expiredCallback?: () => void;
    errorCallback?: () => void;
    className?: string;
    disabled?: boolean;
  }

  const {
    siteKey,
    version = 'v2',
    theme = 'light',
    size = 'normal',
    action = 'submit',
    badge = 'bottomright',
    language = 'th',
    tabindex = 0,
    callback,
    expiredCallback,
    errorCallback,
    className = '',
    disabled = false
  } = $props();

  let container: HTMLDivElement;
  let widgetId: number | null = null;
  let isLoaded = false;

  // ตรวจสอบว่า Google reCAPTCHA script ถูกโหลดแล้วหรือยัง
  function loadRecaptchaScript(): Promise<void> {
    return new Promise((resolve) => {
      if ((window as any).grecaptcha) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}&hl=${language}`;
      script.async = true;
      script.defer = true;
      script.onload = () => resolve();
      document.head.appendChild(script);
    });
  }

  // สร้าง reCAPTCHA v2
  function createV2Recaptcha() {
    if (!(window as any).grecaptcha || !container) return;

    try {
      widgetId = (window as any).grecaptcha.render(container, {
        sitekey: siteKey,
        theme: theme,
        size: size,
        tabindex: tabindex,
        callback: (token: string) => {
          if (callback) callback(token);
        },
        'expired-callback': () => {
          if (expiredCallback) expiredCallback();
        },
        'error-callback': () => {
          if (errorCallback) errorCallback();
        }
      });
    } catch (error) {
      console.error('Error creating reCAPTCHA v2:', error);
      if (errorCallback) errorCallback();
    }
  }

  // สร้าง reCAPTCHA v3
  async function createV3Recaptcha(): Promise<string> {
    if (!(window as any).grecaptcha) {
      await loadRecaptchaScript();
    }

    try {
      const token = await (window as any).grecaptcha.execute(siteKey, { action });
      if (callback) callback(token);
      return token;
    } catch (error) {
      console.error('Error creating reCAPTCHA v3:', error);
      if (errorCallback) errorCallback();
      throw error;
    }
  }

  // รีเซ็ต reCAPTCHA
  export function reset(): void {
    if (version === 'v2' && widgetId !== null && (window as any).grecaptcha) {
      (window as any).grecaptcha.reset(widgetId);
    }
  }

  // รับ token สำหรับ v3
  export async function execute(): Promise<string> {
    if (version === 'v3') {
      return await createV3Recaptcha();
    }
    throw new Error('execute() is only available for reCAPTCHA v3');
  }

  // ตรวจสอบว่า reCAPTCHA ถูกตรวจสอบแล้วหรือยัง (สำหรับ v2)
  export function getResponse(): string {
    if (version === 'v2' && widgetId !== null && (window as any).grecaptcha) {
      return (window as any).grecaptcha.getResponse(widgetId);
    }
    return '';
  }

  onMount(async () => {
    if (disabled) return;

    try {
      await loadRecaptchaScript();
      isLoaded = true;

      if (version === 'v2') {
        createV2Recaptcha();
      } else if (version === 'v3' && size === 'invisible') {
        // สำหรับ v3 ที่เป็น invisible จะไม่แสดง UI
        // ต้องเรียก execute() เอง
      }
    } catch (error) {
      console.error('Error loading reCAPTCHA:', error);
      if (errorCallback) errorCallback();
    }
  });
</script>

<!-- ประกาศ global types สำหรับ TypeScript -->
<script context="module" lang="ts">
  declare global {
    interface Window {
      grecaptcha: {
        render: (container: HTMLElement, options: any) => number;
        reset: (widgetId: number) => void;
        getResponse: (widgetId: number) => string;
        execute: (siteKey: string, options: { action: string }) => Promise<string>;
      };
    }
  }
</script>

<div 
  bind:this={container}
  class="recaptcha-container {className}"
  class:invisible={version === 'v3' && size === 'invisible'}
  class:disabled={disabled}
>
  {#if version === 'v2'}
    <!-- reCAPTCHA v2 จะถูก render ใน container นี้ -->
  {:else if version === 'v3' && size !== 'invisible'}
    <!-- สำหรับ v3 ที่ไม่ใช่ invisible จะแสดง badge -->
    <div class="recaptcha-v3-badge">
      <div class="recaptcha-badge" data-badge="{badge}">
        <div class="recaptcha-logo">
          <svg width="20" height="20" viewBox="0 0 24 24">
            <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
        </div>
        <div class="recaptcha-text">reCAPTCHA</div>
      </div>
    </div>
  {/if}
</div>

<style>
  .recaptcha-container {
    display: inline-block;
    position: relative;
  }

  .recaptcha-container.invisible {
    display: none;
  }

  .recaptcha-container.disabled {
    opacity: 0.5;
    pointer-events: none;
  }

  .recaptcha-v3-badge {
    position: fixed;
    bottom: 14px;
    right: 14px;
    z-index: 2147483647;
  }

  .recaptcha-v3-badge[data-badge="bottomleft"] {
    left: 14px;
    right: auto;
  }

  .recaptcha-v3-badge[data-badge="inline"] {
    position: static;
    display: inline-block;
  }

  .recaptcha-badge {
    display: flex;
    align-items: center;
    background: #f9f9f9;
    border: 1px solid #d3d3d3;
    border-radius: 3px;
    box-shadow: 0 0 4px 1px rgba(0,0,0,0.08);
    padding: 0 4px;
    height: 30px;
    font-family: Roboto, helvetica, arial, sans-serif;
    font-size: 11px;
    color: #000;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .recaptcha-badge:hover {
    box-shadow: 0 0 4px 1px rgba(0,0,0,0.12);
  }

  .recaptcha-logo {
    margin-right: 4px;
  }

  .recaptcha-text {
    color: #555;
    font-weight: 500;
  }

  /* Dark theme */
  .recaptcha-badge[data-theme="dark"] {
    background: #303030;
    border-color: #555;
    color: #fff;
  }

  .recaptcha-badge[data-theme="dark"] .recaptcha-text {
    color: #ccc;
  }
</style> 