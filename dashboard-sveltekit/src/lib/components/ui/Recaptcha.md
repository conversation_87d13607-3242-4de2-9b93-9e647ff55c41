# Google reCAPTCHA Component

Component ที่รองรับ Google reCAPTCHA ทั้ง v2 และ v3 สำหรับป้องกัน bot และ spam

## การใช้งาน

### Basic Usage

```svelte
<script>
  import { Recaptcha } from '$lib/components/ui';
  
  let recaptchaRef;
  let token = '';
  
  function handleSuccess(event) {
    token = event.detail.token;
    console.log('reCAPTCHA success:', token);
  }
  
  function handleError() {
    console.log('reCAPTCHA error');
  }
</script>

<Recaptcha
  bind:this={recaptchaRef}
  siteKey="your-site-key"
  version="v2"
  on:success={handleSuccess}
  on:error={handleError}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `siteKey` | `string` | - | Google reCAPTCHA site key (required) |
| `version` | `'v2' \| 'v3'` | `'v2'` | เวอร์ชันของ reCAPTCHA |
| `theme` | `'light' \| 'dark'` | `'light'` | ธีมของ reCAPTCHA (v2 only) |
| `size` | `'normal' \| 'compact' \| 'invisible'` | `'normal'` | ขนาดของ reCAPTCHA (v2 only) |
| `action` | `string` | `'submit'` | Action สำหรับ v3 |
| `badge` | `'bottomright' \| 'bottomleft' \| 'inline'` | `'bottomright'` | ตำแหน่ง badge (v3 only) |
| `language` | `string` | `'th'` | ภาษาของ reCAPTCHA |
| `tabindex` | `number` | `0` | Tab index |
| `callback` | `(token: string) => void` | - | Callback เมื่อสำเร็จ |
| `expiredCallback` | `() => void` | - | Callback เมื่อหมดอายุ (v2 only) |
| `errorCallback` | `() => void` | - | Callback เมื่อเกิดข้อผิดพลาด |
| `className` | `string` | `''` | CSS class เพิ่มเติม |
| `disabled` | `boolean` | `false` | ปิดการใช้งาน reCAPTCHA |

## Events

| Event | Detail | Description |
|-------|--------|-------------|
| `success` | `{ token: string }` | เรียกเมื่อ reCAPTCHA สำเร็จ |
| `expired` | - | เรียกเมื่อ reCAPTCHA หมดอายุ (v2 only) |
| `error` | - | เรียกเมื่อเกิดข้อผิดพลาด |

## Methods

### reset()
รีเซ็ต reCAPTCHA (v2 only)

```svelte
<script>
  let recaptchaRef;
  
  function resetRecaptcha() {
    recaptchaRef.reset();
  }
</script>

<Recaptcha bind:this={recaptchaRef} siteKey="your-key" />
<button onclick={resetRecaptcha}>Reset</button>
```

### execute()
เรียกใช้ reCAPTCHA v3 (v3 only)

```svelte
<script>
  let recaptchaRef;
  
  async function submitForm() {
    try {
      const token = await recaptchaRef.execute();
      // ส่ง token ไปยัง server
      console.log('Token:', token);
    } catch (error) {
      console.error('reCAPTCHA error:', error);
    }
  }
</script>

<Recaptcha 
  bind:this={recaptchaRef} 
  siteKey="your-key" 
  version="v3" 
/>
<button onclick={submitForm}>Submit</button>
```

### getResponse()
รับ token ของ reCAPTCHA v2 (v2 only)

```svelte
<script>
  let recaptchaRef;
  
  function getToken() {
    const token = recaptchaRef.getResponse();
    console.log('Token:', token);
  }
</script>

<Recaptcha bind:this={recaptchaRef} siteKey="your-key" />
<button onclick={getToken}>Get Token</button>
```

## ตัวอย่างการใช้งาน

### reCAPTCHA v2 Normal

```svelte
<Recaptcha
  siteKey="your-site-key"
  version="v2"
  theme="light"
  size="normal"
  language="th"
  on:success={handleSuccess}
  on:expired={handleExpired}
  on:error={handleError}
/>
```

### reCAPTCHA v2 Compact

```svelte
<Recaptcha
  siteKey="your-site-key"
  version="v2"
  theme="dark"
  size="compact"
  language="th"
  on:success={handleSuccess}
  on:expired={handleExpired}
  on:error={handleError}
/>
```

### reCAPTCHA v2 Invisible

```svelte
<Recaptcha
  siteKey="your-site-key"
  version="v2"
  theme="light"
  size="invisible"
  language="th"
  on:success={handleSuccess}
  on:expired={handleExpired}
  on:error={handleError}
/>
```

### reCAPTCHA v3

```svelte
<Recaptcha
  siteKey="your-site-key"
  version="v3"
  action="submit"
  badge="bottomright"
  language="th"
  on:success={handleSuccess}
  on:error={handleError}
/>
```

### reCAPTCHA v3 with Inline Badge

```svelte
<Recaptcha
  siteKey="your-site-key"
  version="v3"
  action="login"
  badge="inline"
  language="th"
  on:success={handleSuccess}
  on:error={handleError}
/>
```

## การตั้งค่า Server

### Backend Verification (Node.js/Express)

```javascript
import axios from 'axios';

async function verifyRecaptcha(token, secretKey) {
  try {
    const response = await axios.post('https://www.google.com/recaptcha/api/siteverify', {
      secret: secretKey,
      response: token
    });
    
    return response.data.success;
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    return false;
  }
}

// ใช้งาน
app.post('/submit', async (req, res) => {
  const { recaptchaToken } = req.body;
  
  const isValid = await verifyRecaptcha(recaptchaToken, process.env.RECAPTCHA_SECRET);
  
  if (!isValid) {
    return res.status(400).json({ error: 'Invalid reCAPTCHA' });
  }
  
  // ประมวลผลข้อมูล
  res.json({ success: true });
});
```

### Backend Verification (Elysia)

```typescript
import { Elysia } from 'elysia';
import axios from 'axios';

const app = new Elysia()
  .post('/submit', async ({ body }) => {
    const { recaptchaToken } = body;
    
    try {
      const response = await axios.post('https://www.google.com/recaptcha/api/siteverify', {
        secret: process.env.RECAPTCHA_SECRET,
        response: recaptchaToken
      });
      
      if (!response.data.success) {
        return { error: 'Invalid reCAPTCHA' };
      }
      
      // ประมวลผลข้อมูล
      return { success: true };
    } catch (error) {
      console.error('reCAPTCHA verification error:', error);
      return { error: 'Verification failed' };
    }
  });

export default app;
```

## หมายเหตุ

1. **Site Key**: ต้องได้จาก Google reCAPTCHA Console
2. **Secret Key**: ใช้สำหรับตรวจสอบที่ server (ไม่ควรเปิดเผยใน frontend)
3. **Domain**: ต้องตั้งค่า domain ใน Google reCAPTCHA Console
4. **HTTPS**: reCAPTCHA ต้องการ HTTPS ใน production
5. **Language**: รองรับหลายภาษา เช่น 'th', 'en', 'ja' เป็นต้น

## การตั้งค่า Google reCAPTCHA

1. ไปที่ [Google reCAPTCHA Console](https://www.google.com/recaptcha/admin)
2. สร้าง site ใหม่
3. เลือก reCAPTCHA type (v2 หรือ v3)
4. ใส่ domain ที่ต้องการใช้งาน
5. รับ Site Key และ Secret Key
6. ใช้ Site Key ใน frontend และ Secret Key ใน backend 