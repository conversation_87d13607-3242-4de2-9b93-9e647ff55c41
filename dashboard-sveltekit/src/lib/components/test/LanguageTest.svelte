<script lang="ts">
    import { onMount } from "svelte";
    import { t } from "svelte-i18n";
    import { languageStore } from "$lib/stores/language.svelte";
    import { supportedLocales } from "$lib/i18n";
    import { logger, LogCategory } from "$lib/utils/logger";
    import Icon from "@iconify/svelte";

    let currentLanguage = $derived(languageStore.language);
    let testResults = $state<Record<string, any>>({});
    let isTestRunning = $state(false);

    const testKeys = [
        "common.loading",
        "common.save",
        "auth.signin",
        "auth.signout",
        "user.profile",
        "navigation.dashboard",
        "website.websiteManagement",
    ];

    async function runLanguageTest() {
        isTestRunning = true;
        testResults = {};

        logger.info(
            LogCategory.SYSTEM,
            "language_test_started",
            "Language system test started",
            {
                currentLanguage,
            },
        );

        for (const locale of supportedLocales) {
            const results = {
                locale: locale.code,
                name: locale.name,
                translations: {},
                errors: [],
                success: true,
            };

            try {
                // เปลี่ยนภาษา
                languageStore.setLanguage(locale.code as any);

                // รอให้ภาษาโหลดเสร็จ
                await new Promise((resolve) => setTimeout(resolve, 500));

                // ทดสอบ translation keys
                for (const key of testKeys) {
                    try {
                        const translation = $t(key);
                        results.translations[key] = translation;

                        if (translation === key) {
                            results.errors.push(
                                `Missing translation for key: ${key}`,
                            );
                            results.success = false;
                        }
                    } catch (error) {
                        results.errors.push(
                            `Error translating key ${key}: ${error}`,
                        );
                        results.success = false;
                    }
                }

                // ทดสอบ date formatting
                try {
                    const testDate = new Date("2024-01-15");
                    results.dateFormatting = {
                        short: testDate.toLocaleDateString(locale.code),
                        long: testDate.toLocaleDateString(locale.code, {
                            weekday: "long",
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                        }),
                    };
                } catch (error) {
                    results.errors.push(`Date formatting error: ${error}`);
                }

                // ทดสอบ number formatting
                try {
                    const testNumber = 1234.56;
                    results.numberFormatting = {
                        decimal: testNumber.toLocaleString(locale.code),
                        currency: testNumber.toLocaleString(locale.code, {
                            style: "currency",
                            currency: "THB",
                        }),
                    };
                } catch (error) {
                    results.errors.push(`Number formatting error: ${error}`);
                }
            } catch (error) {
                results.errors.push(`General error: ${error}`);
                results.success = false;
            }

            testResults[locale.code] = results;
        }

        // กลับไปภาษาเดิม
        languageStore.setLanguage(currentLanguage);

        logger.info(
            LogCategory.SYSTEM,
            "language_test_completed",
            "Language system test completed",
            {
                results: Object.keys(testResults).map((key) => ({
                    locale: key,
                    success: testResults[key].success,
                    errorCount: testResults[key].errors.length,
                })),
            },
        );

        isTestRunning = false;
    }

    function testCrossTabSync() {
        logger.info(
            LogCategory.SYSTEM,
            "cross_tab_sync_test",
            "Testing cross-tab synchronization",
        );

        // เปลี่ยนภาษาเพื่อทดสอบ sync
        const currentLang = languageStore.language;
        const testLang = currentLang === "th" ? "en" : "th";

        languageStore.setLanguage(testLang);

        setTimeout(() => {
            languageStore.setLanguage(currentLang);
        }, 2000);
    }

    onMount(() => {
        logger.info(
            LogCategory.SYSTEM,
            "language_test_component_mounted",
            "Language test component mounted",
            {
                currentLanguage,
            },
        );
    });
</script>

<div class="space-y-6">
    <div class="card bg-base-100 shadow-sm">
        <div class="card-body">
            <h2 class="card-title">Language System Test</h2>

            <div class="flex gap-4 mb-4">
                <button
                    class="btn btn-primary"
                    onclick={runLanguageTest}
                    disabled={isTestRunning}
                >
                    {#if isTestRunning}
                        <span class="loading loading-spinner loading-sm"></span>
                        Running Tests...
                    {:else}
                        <Icon icon="mdi:test-tube" class="w-4 h-4" />
                        Run Language Tests
                    {/if}
                </button>

                <button class="btn btn-secondary" onclick={testCrossTabSync}>
                    <Icon icon="mdi:sync" class="w-4 h-4" />
                    Test Cross-Tab Sync
                </button>
            </div>

            <div class="alert alert-info">
                <Icon icon="mdi:information" class="w-5 h-5" />
                <div>
                    <p><strong>Current Language:</strong> {currentLanguage}</p>
                    <p>
                        <strong>Test Status:</strong>
                        {isTestRunning ? "Running..." : "Ready"}
                    </p>
                </div>
            </div>
        </div>
    </div>

    {#if Object.keys(testResults).length > 0}
        <div class="space-y-4">
            {#each Object.entries(testResults) as [localeCode, result]}
                <div class="card bg-base-100 shadow-sm">
                    <div class="card-body">
                        <div class="flex items-center gap-2 mb-4">
                            <Icon
                                icon={supportedLocales.find(
                                    (l) => l.code === localeCode,
                                )?.flag || "mdi:flag"}
                                class="w-6 h-6"
                            />
                            <h3 class="text-lg font-semibold">
                                {result.name} ({localeCode})
                            </h3>
                            {#if result.success}
                                <div class="badge badge-success">✓ Pass</div>
                            {:else}
                                <div class="badge badge-error">✗ Fail</div>
                            {/if}
                        </div>

                        {#if result.errors.length > 0}
                            <div class="alert alert-error mb-4">
                                <Icon icon="mdi:alert-circle" class="w-5 h-5" />
                                <div>
                                    <p>
                                        <strong
                                            >Errors ({result.errors
                                                .length}):</strong
                                        >
                                    </p>
                                    <ul class="list-disc list-inside">
                                        {#each result.errors as error}
                                            <li class="text-sm">{error}</li>
                                        {/each}
                                    </ul>
                                </div>
                            </div>
                        {/if}

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Translations -->
                            <div>
                                <h4 class="font-semibold mb-2">Translations</h4>
                                <div class="space-y-1">
                                    {#each Object.entries(result.translations) as [key, value]}
                                        <div
                                            class="flex justify-between text-sm"
                                        >
                                            <span
                                                class="font-mono text-gray-600"
                                                >{key}:</span
                                            >
                                            <span>{value}</span>
                                        </div>
                                    {/each}
                                </div>
                            </div>

                            <!-- Formatting -->
                            <div>
                                <h4 class="font-semibold mb-2">Formatting</h4>
                                {#if result.dateFormatting}
                                    <div class="mb-2">
                                        <p class="text-sm font-medium">
                                            Date Formatting:
                                        </p>
                                        <p class="text-sm">
                                            Short: {result.dateFormatting.short}
                                        </p>
                                        <p class="text-sm">
                                            Long: {result.dateFormatting.long}
                                        </p>
                                    </div>
                                {/if}

                                {#if result.numberFormatting}
                                    <div>
                                        <p class="text-sm font-medium">
                                            Number Formatting:
                                        </p>
                                        <p class="text-sm">
                                            Decimal: {result.numberFormatting
                                                .decimal}
                                        </p>
                                        <p class="text-sm">
                                            Currency: {result.numberFormatting
                                                .currency}
                                        </p>
                                    </div>
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>
            {/each}
        </div>
    {/if}

    <!-- Current Translations Demo -->
    <div class="card bg-base-100 shadow-sm">
        <div class="card-body">
            <h3 class="card-title">Live Translation Demo</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                {#each testKeys as key}
                    <div
                        class="flex justify-between items-center p-2 bg-base-200 rounded"
                    >
                        <span class="font-mono text-sm text-gray-600"
                            >{key}</span
                        >
                        <span class="font-medium">{$t(key)}</span>
                    </div>
                {/each}
            </div>
        </div>
    </div>
</div>
