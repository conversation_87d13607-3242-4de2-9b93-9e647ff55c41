<script lang="ts">
    import { page } from "$app/state";
    import Icon from "@iconify/svelte";
    import { authStore } from "$lib/stores/auth.svelte";
    import { siteStore } from "$lib/stores/site.svelte";
    import Image from "$lib/components/ui/Image.svelte";
    import { toJsonString } from "$lib/utils/debug";
    
    let { data } = $props();
    const siteId = $derived(page.params.siteId);
    const siteData = $derived(data?.site || page.data.site);

    $effect(() => {
        console.log("🔍 Sidebar data prop:", data);
        console.log("🔍 Site data in Sidebar:", siteData);
        console.log("🔍 Page data:", page.data);
    });

    // อัปเดต site store เมื่อมีข้อมูลใหม่
    $effect(() => {
        if (siteData) {
            siteStore.setSite(siteData);
        }
    });

    interface NavItem {
        href: string;
        icon: string;
        label: string;
        adminOnly?: boolean;
    }

    interface NavGroup {
        id: string;
        label: string;
        icon: string;
        href?: string; // สำหรับเมนูลิงก์โดยตรง
        items?: NavItem[]; // สำหรับเมนูที่มีเมนูย่อย
        adminOnly?: boolean;
    }

    // จัดกลุ่มเมนูเป็นหมวดหมู่ตามโครงสร้างที่สร้างไว้
    const navGroups = $derived([
        {
            id: "dashboard",
            href: `/dashboard/${siteId}`,
            icon: "mdi:view-dashboard",
            label: "ภาพรวม",
        },
        {
            id: "analytics",
            label: "การวิเคราะห์",
            icon: "mdi:chart-line",
            href: `/dashboard/${siteId}/analytics`,
        },
        {
            id: "products",
            label: "สินค้า",
            icon: "mdi:package",
            items: [
                {
                    href: `/dashboard/${siteId}/products`,
                    icon: "mdi:package",
                    label: "สินค้าทั้งหมด",
                },
                {
                    href: `/dashboard/${siteId}/categories`,
                    icon: "mdi:folder-multiple",
                    label: "หมวดหมู่",
                },
                {
                    href: `/dashboard/${siteId}/products/brands`,
                    icon: "mdi:tag",
                    label: "แบรนด์",
                },
                {
                    href: `/dashboard/${siteId}/products/reviews`,
                    icon: "mdi:star",
                    label: "รีวิวสินค้า",
                },
            ],
        },
        {
            id: "orders",
            label: "คำสั่งซื้อ",
            icon: "mdi:shopping-cart",
            items: [
                {
                    href: `/dashboard/${siteId}/orders`,
                    icon: "mdi:shopping-cart",
                    label: "คำสั่งซื้อทั้งหมด",
                },
                {
                    href: `/dashboard/${siteId}/orders/preorders`,
                    icon: "mdi:clock-outline",
                    label: "Pre-order",
                },
                {
                    href: `/dashboard/${siteId}/orders/shipping`,
                    icon: "mdi:truck",
                    label: "การจัดส่ง",
                },
            ],
        },
        {
            id: "customers",
            label: "ลูกค้า",
            icon: "mdi:account-group",
            href: `/dashboard/${siteId}/customers`,
        },
        {
            id: "content",
            label: "เนื้อหา",
            icon: "mdi:file-document",
            href: `/dashboard/${siteId}/content`,
        },
        {
            id: "marketing",
            label: "การตลาด",
            icon: "mdi:megaphone",
            href: `/dashboard/${siteId}/marketing`,
        },
        {
            id: "team",
            label: "ทีมงาน",
            icon: "mdi:account-group",
            href: `/dashboard/${siteId}/team`,
        },
        {
            id: "settings",
            label: "การตั้งค่า",
            icon: "mdi:cog",
            items: [
                {
                    href: `/dashboard/${siteId}/settings`,
                    icon: "mdi:cog",
                    label: "ตั้งค่าทั่วไป",
                },
                {
                    href: `/dashboard/${siteId}/themes`,
                    icon: "mdi:palette",
                    label: "ธีมและการแสดงผล",
                },
                {
                    href: `/dashboard/${siteId}/subscriptions`,
                    icon: "mdi:credit-card",
                    label: "การสมัครใช้งาน",
                },
            ],
        },
    ] as NavGroup[]);

    // เก็บสถานะการเปิด/ปิดของแต่ละกลุ่ม
    let expandedGroups = $state(new Set(["dashboard"])); // เปิด dashboard เป็นค่าเริ่มต้น

    const filteredNavGroups = $derived(
        navGroups
            .filter((group) => {
                if (group.adminOnly && !authStore.isAdmin) return false;

                // ถ้าไม่มี items (เช่น dashboard) ให้แสดงเลย
                if (!group.items) return true;

                // กรอง items ในกลุ่มด้วย
                const filteredItems = group.items.filter((item) => {
                    if (item.adminOnly && !authStore.isAdmin) return false;
                    return true;
                });

                // ถ้าไม่มี items ที่เหลือ ให้ซ่อนกลุ่มนี้
                return filteredItems.length > 0;
            })
            .map((group) => ({
                ...group,
                items: group.items
                    ? group.items.filter((item) => {
                          if (item.adminOnly && !authStore.isAdmin)
                              return false;
                          return true;
                      })
                    : [],
            })),
    );

    const currentPath = $derived(page.url.pathname);

    function isActive(href: string): boolean {
        // กรณีพิเศษสำหรับ dashboard หลัก
        if (href === `/dashboard/${siteId}`) {
            return (
                currentPath === `/dashboard/${siteId}` ||
                currentPath === `/dashboard/${siteId}/`
            );
        }

        // กรณีอื่นๆ ตรวจสอบว่า path ตรงกันหรือไม่
        return currentPath === href;
    }

    function isGroupExpanded(groupId: string): boolean {
        return expandedGroups.has(groupId);
    }

    // ตรวจสอบว่ากลุ่มไหนมี active item
    function hasActiveItem(items?: NavItem[]): boolean {
        if (!items) return false;
        return items.some((item) => isActive(item.href));
    }

    // เปิดกลุ่มอัตโนมัติเมื่อมี active item
    $effect(() => {
        const activeGroup = filteredNavGroups.find(
            (group) => group.items && hasActiveItem(group.items),
        );
        if (activeGroup && !expandedGroups.has(activeGroup.id)) {
            expandedGroups.add(activeGroup.id);
            expandedGroups = new Set(expandedGroups);
        }
    });
</script>

<div class="drawer-side">
    <label for="drawer-toggle" class="drawer-overlay"></label>
    <aside class="min-h-full w-64 bg-base-200">
        <!-- Logo -->
        <div class="flex flex-row gap-2 p-1 items-center justify-center">
            <a
                href="/dashboard"
                class="flex flex-col items-center justify-center"
            >
                <Icon
                    icon="solar:undo-left-round-square-line-duotone"
                    class="size-6 text-primary"
                />
            </a>
            <div class="p-1 border-b border-base-300 overflow-hidden">
                <div class="flex items-center gap-2 w-full flex-row">
                    <div class="flex-shrink-0">
                        <Image
                            publicId={siteData?.seoSettings?.logo}
                            alt={siteData?.name}
                            width={40}
                            height={40}
                            class="rounded-full"
                        />
                    </div>
                    <div class="flex flex-col">
                        <span class="text-base font-semibold truncate"
                            >{siteData?.fullDomain}</span
                        >
                        {#if $siteStore.site}
                            <div class="text-sm text-base-content/70">
                                <!-- <p class="font-medium">{$siteStore.site.name}</p> -->
                                <!-- <p class="text-xs">{$siteStore.site.fullDomain}</p> -->
                                <div class="flex gap-1">
                                    {#if $siteStore.site.isActive}
                                        <span
                                            class="badge badge-success badge-sm"
                                            >Active</span
                                        >
                                    {:else}
                                        <span class="badge badge-error badge-sm"
                                            >Inactive</span
                                        >
                                    {/if}
                                    {#if $siteStore.site.isExpired}
                                        <span
                                            class="badge badge-warning badge-sm"
                                            >Expired</span
                                        >
                                    {:else if $siteStore.site.daysUntilExpiry !== undefined}
                                        <span class="badge badge-info badge-sm">
                                            {Math.max(
                                                0,
                                                $siteStore.site.daysUntilExpiry,
                                            )} days
                                        </span>
                                    {/if}
                                </div>
                            </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="p-0">
            <ul class="menu menu-vertical w-full">
                {#each filteredNavGroups as group}
                    <li>
                        {#if group.href}
                            <!-- เมนูลิงก์โดยตรง -->
                            <a
                                href={group.href}
                                class="text-base font-medium {isActive(
                                    group.href,
                                )
                                    ? 'menu-active'
                                    : ''}"
                            >
                                <Icon icon={group.icon} class="size-5" />
                                {group.label}
                            </a>
                        {:else}
                            <!-- เมนูที่มีเมนูย่อย -->
                            <details open={isGroupExpanded(group.id)}>
                                <summary
                                    class="text-base font-medium {hasActiveItem(
                                        group.items,
                                    )
                                        ? 'menu-active'
                                        : ''}"
                                >
                                    <Icon icon={group.icon} class="size-5" />
                                    {group.label}
                                </summary>
                                <ul>
                                    {#each group.items as item}
                                        <li>
                                            <a
                                                href={item.href}
                                                class="text-base font-medium {isActive(
                                                    item.href,
                                                )
                                                    ? 'menu-active'
                                                    : ''}"
                                            >
                                                <Icon
                                                    icon={item.icon}
                                                    class="size-5"
                                                />
                                                {item.label}
                                            </a>
                                        </li>
                                    {/each}
                                </ul>
                            </details>
                        {/if}
                    </li>
                {/each}
            </ul>
        </nav>

        <!-- User Info -->
        {#if authStore.user}
            <div
                class="absolute bottom-0 left-0 right-0 p-4 border-t border-base-300"
            >
            กกกกก
                <!-- <div class="flex items-center gap-3">
                    <div class="avatar placeholder">
                        <div
                            class="bg-neutral text-neutral-content rounded-full w-10"
                        >
                            <span class="text-sm">
                                {authStore.user.firstName.charAt(
                                    0,
                                )}{authStore.user.lastName.charAt(0)}
                            </span>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium truncate">
                            {authStore.user.firstName}
                            {authStore.user.lastName}
                        </p>
                        <p class="text-xs text-base-content/60 truncate">
                            {authStore.user.email}
                        </p>
                    </div>
                </div> -->
            </div>
        {/if}
    </aside>
</div>
