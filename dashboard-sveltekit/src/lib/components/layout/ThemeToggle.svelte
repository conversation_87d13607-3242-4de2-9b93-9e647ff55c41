<script lang="ts">
    import { onMount, onDestroy } from "svelte";
    import { t } from "svelte-i18n";
    import Icon from "@iconify/svelte";
    import { themeStore } from "$lib/stores/theme.svelte";
    import { logger, LogCategory } from "$lib/utils/logger";

    let currentTheme = $derived(themeStore.theme);
    let effectiveTheme = $derived(themeStore.getEffectiveThemeValue());
    let systemPrefersDark = $derived(themeStore.systemPrefersDark);

    onMount(async () => {
        logger.debug(
            LogCategory.SYSTEM,
            "theme_toggle_mounted",
            "ThemeToggle component mounted",
            {
                currentTheme,
                effectiveTheme,
                systemPrefersDark,
            },
        );
    });

    onDestroy(() => {
        logger.debug(
            LogCategory.SYSTEM,
            "theme_toggle_destroyed",
            "ThemeToggle component destroyed",
        );
    });

    function toggleTheme() {
        logger.info(
            LogCategory.SYSTEM,
            "theme_toggle_clicked",
            "Theme toggle button clicked",
            {
                currentTheme,
                effectiveTheme,
            },
        );
        themeStore.toggleTheme();
    }

    function getThemeIcon() {
        switch (currentTheme) {
            case "dark":
                // แสดงไอคอนดวงอาทิตย์เมื่ออยู่ในโหมดมืด (จะเปลี่ยนเป็นสว่าง)
                return "line-md:sunny-filled-loop-to-moon-filled-alt-loop-transition";
            case "light":
                // แสดงไอคอนดวงจันทร์เมื่ออยู่ในโหมดสว่าง (จะเปลี่ยนเป็นมืด)
                return "line-md:moon-filled-alt-to-sunny-filled-loop-transition";
            case "auto":
                // ใช้ไอคอนที่แสดงสถานะปัจจุบันของ auto mode
                return effectiveTheme === "dark"
                    ? "line-md:sunny-filled-loop-to-moon-filled-alt-loop-transition"
                    : "line-md:moon-filled-alt-to-sunny-filled-loop-transition";
            default:
                return "line-md:moon-filled-alt-to-sunny-filled-loop-transition";
        }
    }

    function getAriaLabel() {
        switch (currentTheme) {
            case "dark":
                return $t("theme.switchToAuto") || "เปลี่ยนเป็นโหมดอัตโนมัติ";
            case "light":
                return $t("theme.switchToDark") || "เปลี่ยนเป็นโหมดมืด";
            case "auto":
                return $t("theme.switchToLight") || "เปลี่ยนเป็นโหมดสว่าง";
            default:
                return $t("theme.switchTheme") || "เปลี่ยนธีม";
        }
    }

    function getTooltipText() {
        switch (currentTheme) {
            case "dark":
                return `${$t("theme.currentTheme") || "ธีมปัจจุบัน"}: ${$t("theme.dark") || "มืด"}`;
            case "light":
                return `${$t("theme.currentTheme") || "ธีมปัจจุบัน"}: ${$t("theme.light") || "สว่าง"}`;
            case "auto":
                return `${$t("theme.currentTheme") || "ธีมปัจจุบัน"}: ${$t("theme.auto") || "อัตโนมัติ"} (${effectiveTheme === "dark" ? $t("theme.dark") || "มืด" : $t("theme.light") || "สว่าง"})`;
            default:
                return $t("theme.toggleTheme") || "เปลี่ยนธีม";
        }
    }
</script>

<div class="tooltip tooltip-bottom" data-tip={getTooltipText()}>
    <button
        type="button"
        onclick={toggleTheme}
        class="btn btn-circle btn-soft relative"
        aria-label={getAriaLabel()}
    >
        <Icon icon={getThemeIcon()} class="size-7" />

        <!-- Auto mode indicator -->
        {#if currentTheme === "auto"}
            <div
                class="absolute -top-1 -right-1 w-5 h-5 bg-primary rounded-full flex items-center justify-center"
            >
                <Icon
                    icon="mdi:auto-mode"
                    class="size-5 text-primary-content"
                />
            </div>
        {/if}
    </button>
</div>
