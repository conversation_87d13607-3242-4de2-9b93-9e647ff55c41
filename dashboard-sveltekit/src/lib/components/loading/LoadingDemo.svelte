<script lang="ts">
    import LoadingScreen from './LoadingScreen.svelte';
    import { loading, loadingStore } from '$lib/stores/loading.svelte';
    import { loadingPresets, type LoadingPreset } from '$lib/config/loading';

    // Demo state
    let demoType = $state<'spinner' | 'dots' | 'bars' | 'pulse' | 'skeleton' | 'progress'>('spinner');
    let demoSize = $state<'sm' | 'md' | 'lg' | 'xl'>('lg');
    let demoColor = $state<'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error'>('primary');
    let demoMessage = $state('กำลังโหลด...');
    let demoProgress = $state(0);
    let showProgress = $state(false);
    let overlay = $state(true);
    let blur = $state(false);
    let animation = $state<'fade' | 'slide-up' | 'scale' | 'none'>('fade');
    let duration = $state(300);

    // Demo loading state
    let isDemoLoading = $state(false);

    // Available options
    const typeOptions = [
        { value: 'spinner', label: 'Spinner', description: 'Classic spinning loader' },
        { value: 'dots', label: 'Dots', description: 'Three bouncing dots' },
        { value: 'bars', label: 'Bars', description: 'Animated bars' },
        { value: 'pulse', label: 'Pulse', description: 'Pulsing circle' },
        { value: 'skeleton', label: 'Skeleton', description: 'Skeleton placeholder' },
        { value: 'progress', label: 'Progress', description: 'Circular progress' },
    ] as const;

    const sizeOptions = [
        { value: 'sm', label: 'Small' },
        { value: 'md', label: 'Medium' },
        { value: 'lg', label: 'Large' },
        { value: 'xl', label: 'Extra Large' },
    ] as const;

    const colorOptions = [
        { value: 'primary', label: 'Primary' },
        { value: 'secondary', label: 'Secondary' },
        { value: 'accent', label: 'Accent' },
        { value: 'info', label: 'Info' },
        { value: 'success', label: 'Success' },
        { value: 'warning', label: 'Warning' },
        { value: 'error', label: 'Error' },
    ] as const;

    const animationOptions = [
        { value: 'fade', label: 'Fade' },
        { value: 'slide-up', label: 'Slide Up' },
        { value: 'scale', label: 'Scale' },
        { value: 'none', label: 'None' },
    ] as const;

    // Demo functions
    function startDemo() {
        isDemoLoading = true;
        demoProgress = 0;
    }

    function stopDemo() {
        isDemoLoading = false;
        demoProgress = 0;
    }

    function simulateProgress() {
        if (!isDemoLoading) return;
        
        const interval = setInterval(() => {
            demoProgress += Math.random() * 15;
            if (demoProgress >= 100) {
                demoProgress = 100;
                clearInterval(interval);
                setTimeout(() => {
                    stopDemo();
                }, 1000);
            }
        }, 200);
    }

    // Global loading demos
    async function demoGlobalLoading() {
        const id = loading.show('กำลังโหลดข้อมูล...', {
            type: demoType,
            size: demoSize,
            color: demoColor,
            overlay: true,
            blur: true
        });

        // Simulate async operation
        await new Promise(resolve => setTimeout(resolve, 3000));
        loading.hide(id);
    }

    async function demoProgressLoading() {
        await loading.withProgress(async (updateProgress) => {
            for (let i = 0; i <= 100; i += 10) {
                updateProgress(i, `กำลังประมวลผล... ${i}%`);
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }, 'กำลังอัปโหลดไฟล์...', {
            type: 'progress',
            size: 'xl',
            color: 'success'
        });
    }

    async function demoPresetLoading(preset: LoadingPreset) {
        const id = loadingStore.showWithPreset(preset);
        await new Promise(resolve => setTimeout(resolve, 2000));
        loading.hide(id);
    }

    // Async operation demo
    async function demoAsyncOperation() {
        try {
            const result = await loading.withLoading(
                new Promise(resolve => setTimeout(() => resolve('Success!'), 2000)),
                'กำลังเชื่อมต่อเซิร์ฟเวอร์...',
                { type: 'pulse', color: 'info' }
            );
            console.log('Operation result:', result);
        } catch (error) {
            console.error('Operation failed:', error);
        }
    }
</script>

<div class="loading-demo p-6 space-y-8">
    <div class="demo-header">
        <h2 class="text-3xl font-bold mb-4">🔄 Enhanced Loading Screen Demo</h2>
        <p class="text-base-content/70 mb-6">
            ทดสอบระบบ Loading Screen ที่ยืดหยุ่นและปรับแต่งได้ผ่าน Environment Variables
        </p>
    </div>

    <!-- Local Demo Controls -->
    <div class="demo-section">
        <h3 class="text-xl font-semibold mb-4">🎛️ Local Loading Demo</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-base-200 rounded-lg mb-4">
            <!-- Type Selection -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Loading Type</span>
                </label>
                <select class="select select-bordered" bind:value={demoType}>
                    {#each typeOptions as option}
                        <option value={option.value}>{option.label}</option>
                    {/each}
                </select>
                <label class="label">
                    <span class="label-text-alt text-xs">
                        {typeOptions.find(t => t.value === demoType)?.description}
                    </span>
                </label>
            </div>

            <!-- Size Selection -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Size</span>
                </label>
                <select class="select select-bordered" bind:value={demoSize}>
                    {#each sizeOptions as option}
                        <option value={option.value}>{option.label}</option>
                    {/each}
                </select>
            </div>

            <!-- Color Selection -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Color</span>
                </label>
                <select class="select select-bordered" bind:value={demoColor}>
                    {#each colorOptions as option}
                        <option value={option.value}>{option.label}</option>
                    {/each}
                </select>
            </div>

            <!-- Message -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Message</span>
                </label>
                <input 
                    type="text" 
                    class="input input-bordered" 
                    bind:value={demoMessage}
                    placeholder="Loading message..."
                />
            </div>

            <!-- Progress -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Progress ({demoProgress}%)</span>
                </label>
                <input 
                    type="range" 
                    min="0" 
                    max="100" 
                    class="range range-primary" 
                    bind:value={demoProgress}
                    disabled={!showProgress}
                />
            </div>

            <!-- Options -->
            <div class="form-control">
                <label class="label cursor-pointer">
                    <span class="label-text">Show Progress</span>
                    <input type="checkbox" class="toggle toggle-primary" bind:checked={showProgress} />
                </label>
                <label class="label cursor-pointer">
                    <span class="label-text">Overlay</span>
                    <input type="checkbox" class="toggle toggle-secondary" bind:checked={overlay} />
                </label>
                <label class="label cursor-pointer">
                    <span class="label-text">Blur</span>
                    <input type="checkbox" class="toggle toggle-accent" bind:checked={blur} />
                </label>
            </div>
        </div>

        <!-- Demo Buttons -->
        <div class="flex flex-wrap gap-2 mb-4">
            <button class="btn btn-primary" onclick={startDemo}>
                🎬 Start Demo
            </button>
            <button class="btn btn-secondary" onclick={stopDemo}>
                ⏹️ Stop Demo
            </button>
            <button class="btn btn-accent" onclick={simulateProgress}>
                📊 Simulate Progress
            </button>
        </div>

        <!-- Local Demo Area -->
        <div class="demo-area border-2 border-dashed border-base-300 rounded-lg min-h-[300px] relative">
            <LoadingScreen
                isLoading={isDemoLoading}
                type={demoType}
                size={demoSize}
                color={demoColor}
                message={demoMessage}
                showProgress={showProgress}
                progress={demoProgress}
                overlay={overlay}
                blur={blur}
                animation={animation}
                duration={duration}
            >
                <div class="p-8 flex flex-col items-center justify-center h-full">
                    <div class="text-4xl mb-4">📱</div>
                    <h4 class="text-xl font-bold mb-2">Demo Content</h4>
                    <p class="text-center text-base-content/70">
                        นี่คือเนื้อหาที่จะถูกซ่อนเมื่อมี loading screen
                    </p>
                </div>
            </LoadingScreen>
        </div>
    </div>

    <!-- Global Loading Demos -->
    <div class="demo-section">
        <h3 class="text-xl font-semibold mb-4">🌐 Global Loading Store Demo</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <button class="btn btn-info" onclick={demoGlobalLoading}>
                🔄 Global Loading
            </button>
            <button class="btn btn-success" onclick={demoProgressLoading}>
                📊 Progress Loading
            </button>
            <button class="btn btn-warning" onclick={demoAsyncOperation}>
                ⚡ Async Operation
            </button>
        </div>

        <div class="mt-4">
            <h4 class="font-semibold mb-2">Preset Demos:</h4>
            <div class="flex flex-wrap gap-2">
                {#each Object.keys(loadingPresets) as preset}
                    <button 
                        class="btn btn-outline btn-sm" 
                        onclick={() => demoPresetLoading(preset as LoadingPreset)}
                    >
                        {preset}
                    </button>
                {/each}
            </div>
        </div>
    </div>

    <!-- Environment Variables Info -->
    <div class="env-info bg-info/10 p-4 rounded-lg">
        <h4 class="font-semibold text-info mb-2">🔧 Environment Variables</h4>
        <div class="text-sm space-y-1 text-info/80">
            <p><code>PUBLIC_LOADING_TYPE</code> - Loading animation type</p>
            <p><code>PUBLIC_LOADING_SIZE</code> - Loading size (sm, md, lg, xl)</p>
            <p><code>PUBLIC_LOADING_COLOR</code> - Loading color theme</p>
            <p><code>PUBLIC_LOADING_DISABLED</code> - Disable all loading screens</p>
            <p><code>PUBLIC_LOADING_ANIMATION</code> - Entrance animation</p>
            <p><code>PUBLIC_LOADING_DURATION</code> - Animation duration (ms)</p>
        </div>
    </div>
</div>

<style>
    .loading-demo {
        contain: layout style paint;
    }
    
    .demo-area {
        position: relative;
        overflow: hidden;
    }
    
    code {
        background: hsl(var(--b3));
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-size: 0.875em;
        font-family: 'Courier New', monospace;
    }
</style>
