# 🔄 Enhanced Loading System

ระบบ Loading Screen ที่ยืดหยุ่นและปรับแต่งได้ผ่าน Environment Variables พร้อม Global State Management

## 🚀 Features

### ✨ Loading Types
- **Spinner**: Classic spinning loader
- **Dots**: Three bouncing dots animation
- **Bars**: Animated vertical bars
- **Pulse**: Pulsing circle effect
- **Skeleton**: Skeleton placeholder loading
- **Progress**: Circular progress indicator

### 🎨 Customization Options
- **6 Loading Types** พร้อม animations ที่แตกต่าง
- **4 Size Options**: sm, md, lg, xl
- **7 Color Themes**: primary, secondary, accent, info, success, warning, error
- **4 Animation Types**: fade, slide-up, scale, none
- **Progress Tracking** พร้อม percentage display
- **Overlay & Blur Effects** สำหรับ full-screen loading

### ⚙️ Environment Configuration
ปรับแต่งได้ผ่าน `.env` variables โดยไม่ต้องแก้ไขโค้ด

### 🌐 Global State Management
- **Loading Queue System** สำหรับจัดการ multiple loading states
- **Priority System** สำหรับ loading ที่สำคัญ
- **Auto-hide** เมื่อ progress ถึง 100%
- **Minimum Display Time** เพื่อป้องกัน flashing

## 📦 Components

### LoadingScreen
Component หลักสำหรับแสดง loading screen

```svelte
<LoadingScreen 
  isLoading={true}
  type="spinner"
  size="lg"
  color="primary"
  message="กำลังโหลด..."
  showProgress={false}
  progress={0}
  overlay={true}
  blur={false}
  animation="fade"
  duration={300}
>
  <YourContent />
</LoadingScreen>
```

### LoadingStore
Global state management สำหรับ loading states

```typescript
import { loading, loadingStore } from '$lib/stores/loading.svelte';

// Basic usage
const id = loading.show('กำลังโหลด...');
loading.hide(id);

// With options
const id = loading.show('กำลังบันทึก...', {
  type: 'spinner',
  color: 'success',
  overlay: true
});

// Preset methods
loading.saving('กำลังบันทึกข้อมูล...');
loading.uploading('กำลังอัปโหลดไฟล์...');
loading.processing('กำลังประมวลผล...');
```

## 🔧 Environment Variables

### Basic Configuration
```env
# Loading Type: spinner, dots, bars, pulse, skeleton, progress
PUBLIC_LOADING_TYPE="spinner"

# Loading Size: sm, md, lg, xl
PUBLIC_LOADING_SIZE="lg"

# Loading Color: primary, secondary, accent, info, success, warning, error
PUBLIC_LOADING_COLOR="primary"
```

### Display Settings
```env
# Default message
PUBLIC_LOADING_MESSAGE="กำลังโหลด..."

# Show progress bar/percentage
PUBLIC_LOADING_SHOW_PROGRESS="false"

# Full screen overlay
PUBLIC_LOADING_OVERLAY="true"

# Blur background
PUBLIC_LOADING_BLUR="false"
```

### Animation Settings
```env
# Animation type: fade, slide-up, scale, none
PUBLIC_LOADING_ANIMATION="fade"

# Animation duration in milliseconds
PUBLIC_LOADING_DURATION="300"
```

### Control Settings
```env
# Disable all loading screens
PUBLIC_LOADING_DISABLED="false"

# Minimum time to show loading (prevent flashing)
PUBLIC_LOADING_MIN_DISPLAY_TIME="500"

# Auto hide when progress reaches 100%
PUBLIC_LOADING_AUTO_HIDE="false"
PUBLIC_LOADING_AUTO_HIDE_DELAY="3000"
```

### Custom Messages
```env
PUBLIC_LOADING_MSG_LOADING="กำลังโหลด..."
PUBLIC_LOADING_MSG_PROCESSING="กำลังประมวลผล..."
PUBLIC_LOADING_MSG_SAVING="กำลังบันทึก..."
PUBLIC_LOADING_MSG_UPLOADING="กำลังอัปโหลด..."
PUBLIC_LOADING_MSG_DOWNLOADING="กำลังดาวน์โหลด..."
PUBLIC_LOADING_MSG_CONNECTING="กำลังเชื่อมต่อ..."
PUBLIC_LOADING_MSG_AUTHENTICATING="กำลังตรวจสอบสิทธิ์..."
PUBLIC_LOADING_MSG_INITIALIZING="กำลังเริ่มต้น..."
```

## 🎯 Usage Examples

### Basic Loading
```svelte
<script>
  import LoadingScreen from '$lib/components/loading/LoadingScreen.svelte';
  
  let isLoading = $state(false);
  
  async function loadData() {
    isLoading = true;
    try {
      await fetchData();
    } finally {
      isLoading = false;
    }
  }
</script>

<LoadingScreen {isLoading} message="กำลังโหลดข้อมูล...">
  <YourContent />
</LoadingScreen>
```

### Global Loading with Store
```svelte
<script>
  import { loading } from '$lib/stores/loading.svelte';
  
  async function saveData() {
    const id = loading.saving('กำลังบันทึกข้อมูล...');
    try {
      await api.save(data);
    } finally {
      loading.hide(id);
    }
  }
  
  // หรือใช้ wrapper function
  async function saveDataWithWrapper() {
    await loading.withLoading(
      api.save(data),
      'กำลังบันทึกข้อมูล...',
      { type: 'spinner', color: 'success' }
    );
  }
</script>
```

### Progress Loading
```svelte
<script>
  import { loading } from '$lib/stores/loading.svelte';
  
  async function uploadFile(file) {
    await loading.withProgress(async (updateProgress) => {
      const chunks = splitFile(file);
      
      for (let i = 0; i < chunks.length; i++) {
        await uploadChunk(chunks[i]);
        const progress = ((i + 1) / chunks.length) * 100;
        updateProgress(progress, `อัปโหลด ${Math.round(progress)}%`);
      }
    }, 'กำลังอัปโหลดไฟล์...', {
      type: 'progress',
      size: 'xl',
      color: 'success'
    });
  }
</script>
```

### Preset Loading
```svelte
<script>
  import { loadingStore } from '$lib/stores/loading.svelte';
  
  // ใช้ preset configurations
  async function initializeApp() {
    const id = loadingStore.showWithPreset('fullscreen', 'กำลังเริ่มต้นระบบ...');
    await initialize();
    loading.hide(id);
  }
</script>
```

## 🎨 Loading Presets

### Available Presets
```typescript
// Fast loading for quick operations
loading.showWithPreset('fast', 'กำลังโหลด...');

// Progress loading for file operations  
loading.showWithPreset('progress', 'กำลังอัปโหลด...');

// Skeleton loading for content
loading.showWithPreset('skeleton');

// Minimal loading
loading.showWithPreset('minimal');

// Full screen loading
loading.showWithPreset('fullscreen', 'กำลังเริ่มต้น...');
```

## 🔄 Loading Queue System

```typescript
// Multiple loading states with priority
const id1 = loading.show('Loading 1...', { priority: 1 });
const id2 = loading.show('Loading 2...', { priority: 2 }); // Higher priority
const id3 = loading.show('Loading 3...', { priority: 1 });

// Loading 2 will be shown first due to higher priority
```

## 📱 Responsive & Accessibility

### Reduced Motion Support
```css
@media (prefers-reduced-motion: reduce) {
  /* Animations are automatically disabled */
}
```

### Mobile Optimization
- Responsive sizing
- Touch-friendly controls
- Optimized animations for mobile devices

## 🚀 Performance Tips

1. **ใช้ `disabled` env variable** เพื่อปิด loading ใน production
2. **ตั้งค่า `minDisplayTime`** เพื่อป้องกัน flashing
3. **ใช้ appropriate loading types** สำหรับ use case ที่แตกต่าง
4. **ใช้ queue system** สำหรับ multiple loading states

## 🐛 Troubleshooting

### Loading ไม่แสดง
- ตรวจสอบ `PUBLIC_LOADING_DISABLED` env variable
- ตรวจสอบ `isLoading` state
- ตรวจสอบ CSS z-index conflicts

### Performance Issues
- ลด animation duration
- ใช้ `type="spinner"` สำหรับ performance ที่ดีที่สุด
- ปิด blur effect บนอุปกรณ์ช้า

### Environment Variables ไม่ทำงาน
- ตรวจสอบ prefix `PUBLIC_` สำหรับ client-side variables
- Restart development server หลังเปลี่ยน env variables
- ตรวจสอบ `.env` file location

## 🔧 Advanced Usage

### Custom Loading Component
```svelte
<script>
  import { loadingConfig } from '$lib/config/loading';
  
  // Override default config
  const customConfig = {
    ...loadingConfig,
    type: 'pulse',
    color: 'accent',
    duration: 500
  };
</script>
```

### Integration with API Calls
```typescript
// Auto-loading wrapper for API calls
const apiWithLoading = {
  async get(url: string) {
    return loading.withLoading(
      fetch(url).then(r => r.json()),
      'กำลังโหลดข้อมูล...'
    );
  },
  
  async post(url: string, data: any) {
    return loading.withLoading(
      fetch(url, { method: 'POST', body: JSON.stringify(data) }),
      'กำลังบันทึกข้อมูล...',
      { type: 'spinner', color: 'success' }
    );
  }
};
```
