<script lang="ts">
    import { goto } from "$app/navigation";
    import { onMount } from "svelte";
    import { t } from "svelte-i18n";
    import Icon from "@iconify/svelte";
    import { authStore } from "$lib/stores/auth.svelte";
    import Button from "$lib/components/ui/Button.svelte";
    import Input from "$lib/components/ui/Input.svelte";
    import SEO from "$lib/components/layout/SEO.svelte";
    import Card from "$lib/components/ui/Card.svelte";
    import { enhance } from "$app/forms";
    import { showSuccess, showError } from "$lib/utils/sweetalert";
    import { forgotPasswordSchema } from '$lib/schemas/auth.schema';

    let { form } = $props<{
        form?: any;
    }>();

    let email = $state("");
    let errors = $state<Record<string, string>>({});

    // Form action result
    let formResult = $derived(form);

    onMount(() => {
        // ถ้า login แล้วให้ redirect ไป dashboard
        if (authStore.isAuthenticated) {
            goto("/dashboard");
        }
    });

    // Handle form result
    $effect(() => {
        if (formResult?.success) {
            showSuccess("สำเร็จ!", formResult.message);
            setTimeout(() => {
                goto("/signin");
            }, 2000);
        } else if (formResult?.error) {
            showError("เกิดข้อผิดพลาด", formResult.error);
        }
    });

    function validateForm(): boolean {
        const result = forgotPasswordSchema.safeParse({ email });
        if (result.success) {
            errors = {};
            return true;
        } else {
            const newErrors: Record<string, string> = {};
            result.error.issues.forEach((issue) => {
                const path = issue.path.join('.');
                if (path) {
                    newErrors[path] = issue.message;
                }
            });
            errors = newErrors;
            return false;
        }
    }

    function handleKeydown(event: KeyboardEvent) {
        if (event.key === "Enter") {
            const form = event.target?.closest('form') as HTMLFormElement;
            if (form) {
                form.requestSubmit();
            }
        }
    }
</script>

<SEO title={$t("auth.forgotPasswordTitle")} />

<Card centered variant="default" shadow="lg" size="sm" title={$t("auth.forgotPasswordTitle")}>
    <!-- Form Action Messages -->
    {#if formResult?.success}
        <div class="alert alert-success">
            <Icon icon="mdi:check-circle" class="w-5 h-5" />
            <span>{formResult.message}</span>
        </div>
    {:else if formResult?.error}
        <div class="alert alert-error">
            <Icon icon="mdi:alert-circle" class="w-5 h-5" />
            <span>{formResult.error}</span>
        </div>
    {/if}

    <!-- Forgot Password Form -->
    <form method="POST" action="?/forgotPassword" class="space-y-6" use:enhance={() => {
        return async ({ result }) => {
            if (result.type === 'failure') {
                showError("เกิดข้อผิดพลาด", result.data?.error || 'ไม่สามารถส่งอีเมลรีเซ็ตรหัสผ่านได้');
            }
        };
    }}>
        <div class="text-center mb-6">
            <Icon icon="mdi:lock-reset" class="w-16 h-16 text-primary mx-auto mb-4" />
            <h2 class="text-xl font-semibold mb-2">{$t("auth.forgotPasswordTitle")}?</h2>
            <p class="text-base-content/70">
                {$t("auth.forgotPasswordDescription")}
            </p>
        </div>

        <Input
            id="email"
            name="email"
            type="email"
            bind:value={email}
            label={$t("auth.email")}
            placeholder="<EMAIL>"
            icon="mdi:email"
            error={errors.email}
            required
            autocomplete="email"
            onkeydown={handleKeydown}
        />

        <Button
            type="submit"
            color="primary"
            size="lg"
            block
            loading={authStore.isLoading}
            disabled={authStore.isLoading}
        >
            {$t("auth.sendResetEmail")}
        </Button>
    </form>

    <!-- Back to Login Link -->
    <div class="text-center mt-6">
        <span class="text-base-content/60">{$t("auth.rememberPassword")} </span>
        <a href="/signin" class="link link-primary">
            {$t("auth.signin")}
        </a>
    </div>
</Card>
