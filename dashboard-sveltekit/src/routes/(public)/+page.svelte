<script lang="ts">
	import { t } from "svelte-i18n";
	import SEO from "$lib/components/layout/SEO.svelte";
	import Icon from "@iconify/svelte";

	const skillListsNew = [
		{
			title: "เริ่มต้นได้อย่างรวดเร็ว",
			icon: "🚁",
			detail: "สมัคร และเปิดใช้งานเว็บไซต์ผ่านระบบอัตโนมัติบนเว็บไซต์ได้ง่ายๆ ไม่เกิน 3 นาทีก็พร้อมใช้งานแล้ว!",
		},
		{
			title: "จัดการสินค้าได้ง่าย",
			icon: "👩‍💻",
			detail: "จัดการสินค้า ตั้งค่า ปรับแต่ง และดูผลรายงานความเคลื่อนไหวต่างๆ ได้จากหน้าเดียว",
		},
		{
			title: "ราคาเริ่มต้นแค่ 29 บาท",
			icon: "⭐",
			detail: "สามารถเช่าวันใช้งานได้ไม่จำกัด ราคาเริ่มต้น แค่ 29 บาท คุ้มค่ามาก",
		},
		{
			title: $t("home.autoTopup"),
			icon: "🏪",
			detail: $t("home.autoTopupDetail"),
		},
		{
			title: "โดเมน หรือ ซัพโดเมน",
			icon: "🌏",
			detail: "ใช้งานซัพโดเมนจากเราได้ฟรี หรือสามารถใช้โดเมนส่วนตัวได้ง่ายๆ โดยมาพร้อม SSL มาให้ฟรี พร้อมต่ออายุหายห่วง",
		},
		{
			title: "สินค้า และหมวดหมู่ ไม่จำกัด",
			icon: "📦",
			detail: "รองรับการเพิ่มสินค้า สต็อคสินค้า และสร้างหมวดหมูสินค้าได้อย่างอิสระ และไม่จำกัดจำนวน",
		},
		{
			title: $t("home.multiProduct"),
			icon: "🎒",
			detail: $t("home.multiProductDetail"),
		},
		{
			title: "ระบบสมาชิก สะสมแต้ม และโปรไฟล์",
			icon: "🏅",
			detail: "มีระบบสมาชิกสมบูรณ์ที่สุด สามารถให้สมาชิกเติมสะสมแต้ม ระบบนายหน้าสมาชิกช่วยขายได้ส่วนแบ่ง พร้อมโปรไฟล์เพื่อดูประวัติและรายละเอียดต่างๆ ของสมาชิก",
		},
		{
			title: "ระบบเกมให้เลือกมากมาย",
			icon: "🎲",
			detail: "มีระบบเกม เช่น กล่องสุ่ม, วงล้อมหาสนุก, ลานเลื่อนมหาสนุก และอื่นๆ",
		},
		{
			title: "Style เว็บไซต์",
			icon: "🎭",
			detail: "รองรับการเลือกใช้ Style เว็บไซต์ได้เอง มีมากมาย และเพิ่มเรื่อยๆ ในอนาคต รวมทั้งสามารถ ปรับแต่งได้อย่างอิสระ",
		},
		{
			title: "ทดลองระบบ ในอนาคต",
			icon: "🔕",
			detail: "เว็บไซต์มีการพัฒนาอยู่อย่างต่อเนื่อง ผู้ใช้บริการจึงสามารถเลือกทดลองระบบใหม่ๆได้ง่ายๆ เพียงปิด-เปิด การทำงานของระบบ นั้นๆ ในคลิกเดียว",
		},
		{
			title: $t("home.support"),
			icon: "❓",
			detail: $t("home.supportDetail"),
		},
	];
</script>

<SEO
	title={$t("home.title")}
	description={$t("home.description")}
	keywords="เช่าใช้งานเว็บไซต์, ระบบเช่าใช้งานเว็บไซต์, ระบบครบครัน, ระบบปลอดภัย"
	url="/"
	noindex={false}
/>

<div class="max-w-7xl mx-auto">
	<div class="text-center">
		<h1 class="text-2xl font-bold tracking-tight sm:text-4xl">
			{$t("home.title")}
		</h1>
		<p class="mt-6 text-lg leading-8">
			{$t("home.description")}
		</p>

		<!-- Section: Navigation Links -->
		<div class="flex flex-col w-full sm:flex-row gap-3 mx-auto my-5">
			<a
				href="/signup"
				class="btn-custom-home from-indigo-500 via-purple-500 to-pink-400"
			>
				<Icon
					icon="solar:user-plus-rounded-line-duotone"
					class="size-10"
				/>
				<span>{$t("auth.signup")}</span>
			</a>
			<a
				href="/signin"
				class="btn-custom-home from-indigo-500 via-sky-500 to-emerald-400"
			>
				<Icon icon="solar:login-3-line-duotone" class="size-10" />
				<span>{$t("auth.signin")}</span>
			</a>
			<a
				href="/help"
				class="btn-custom-home from-red-500 via-orange-400 to-yellow-400"
			>
				<Icon icon="solar:book-2-line-duotone" class="size-10" />
				<span>{$t("auth.help")}</span>
			</a>
		</div>
	</div>

	<div class="mx-auto max-w-2xl lg:text-center space-y-1 my-5">
		<p class="font-bold tracking-tight sm:text-xl">
			{$t("home.subtitle")}
		</p>
		<p class="leading-8">
			{$t("home.subtitleDescription")}
		</p>
	</div>
</div>

<div class="max-w-7xl mx-auto">
	<!-- Section: Skill Lists -->
	<div class="grid gap-4 grid-cols-1 md:grid-cols-2">
		{#each skillListsNew as item, i}
			<div
				class="flex flex-row gap-2 md:gap-4 p-2 md:p-4 rounded-lg bg-base-200"
			>
				<span class="text-3xl md:text-4xl">{item.icon}</span>
				<div class="flex flex-1 flex-col">
					<span class="text-base md:text-lg font-medium"
						>{item.title}</span
					>
					<span class="text-sm md:text-base font-light"
						>{item.detail}</span
					>
				</div>
			</div>
		{/each}
	</div>
</div>

<style>
	/* @import "tailwindcss"; */
	@reference "../../app.css";

	.btn-custom-home {
		@apply flex justify-center items-center p-5 bg-gradient-to-tr hover:scale-102 transition-all duration-300 h-14 md:h-16 gap-1 text-white rounded-lg opacity-90 w-full;
	}

	.btn-custom-home span {
		@apply text-lg md:text-xl font-semibold;
	}
</style>
