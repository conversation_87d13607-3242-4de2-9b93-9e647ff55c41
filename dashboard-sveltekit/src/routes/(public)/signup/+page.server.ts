import type { Actions, PageServerLoad } from './$types';
import { authService } from '$lib/services/auth';
import { redirect, fail } from '@sveltejs/kit';
import { validateSignupData } from '$lib/schemas/auth.schema';

export const prerender = false;

export const load: PageServerLoad = async ({ locals }) => {
  // ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  return {
    user: null
  };
};

export const actions: Actions = {
  /**
   * ✅ User Signup - Hybrid Approach
   * Route API + Service Pattern
   */
  signup: async ({ request, cookies }) => {
    try {
      const formData = await request.formData();

      // Extract and validate form data at route level
      const signupData = {
        email: formData.get('email') as string,
        password: formData.get('password') as string,
        confirmPassword: formData.get('confirmPassword') as string
      };

      // Validate input using Zod
      const validation = validateSignupData(signupData);
      if (!validation.success) {
        return fail(400, {
          message: validation.error || 'ข้อมูลไม่ถูกต้อง',
          type: 'signup'
        });
      }


 

      // Call auth service for business logic + backend API
      const result = await authService.signup({
        email: signupData.email.trim(),
        password: signupData.password.trim(),
        confirmPassword: signupData.confirmPassword.trim()
      });

      console.log('Signup result:', result);

      if (!result.success) {
        return fail(400, {
          message: result.error || result.message || 'ลงทะเบียนล้มเหลว',
          type: 'signup'
        });
      }

      // Set cookies for successful signup (if needed)
      if (result.data?.token) {
        const cookieOptions = {
          path: '/',
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict' as const,
          maxAge: 60 * 60 * 24 * 7 // 7 วัน
        };

        cookies.set('auth_token', result.data.token, cookieOptions);
      }

      return {
        success: true,
        user: result.data?.user,
        message: 'ลงทะเบียนสำเร็จ กรุณาตรวจสอบอีเมลเพื่อยืนยันบัญชี',
        type: 'signup'
      };

    } catch (error) {
      console.error('Signup action error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลงทะเบียน กรุณาลองใหม่อีกครั้ง',
        type: 'signup'
      });
    }
  }
}; 