<script lang="ts">
    import { goto } from '$app/navigation';
    import { enhance } from '$app/forms';
    import { t } from "svelte-i18n";
    import { showSuccess, showError, showLoading } from '$lib/utils/sweetalert';
    import { resetPasswordSchema } from '$lib/schemas/auth.schema';
    import Button from '$lib/components/ui/Button.svelte';
    import Input from '$lib/components/ui/Input.svelte';
    import Card from '$lib/components/ui/Card.svelte';
    import SEO from '$lib/components/layout/SEO.svelte';
    import Icon from "@iconify/svelte";

    let { url, form } = $props();

    let password = $state('');
    let confirmPassword = $state('');
    let isLoading = $state(false);
    let token = $state(url.searchParams.get('token') || '');
    let errors = $state<Record<string, string>>({});

    // Form action result
    let formResult = $derived(form);

    // Handle form result
    $effect(() => {
        if (formResult?.success) {
            showSuccess("สำเร็จ!", formResult.message);
            setTimeout(() => {
                goto('/signin');
            }, 2000);
        } else if (formResult?.error) {
            showError("เกิดข้อผิดพลาด", formResult.error);
        }
    });

    function validateForm(): boolean {
        const result = resetPasswordSchema.safeParse({
            password,
            confirmPassword,
            token
        });

        if (result.success) {
            errors = {};
            return true;
        } else {
            const newErrors: Record<string, string> = {};
            result.error.issues.forEach((issue) => {
                const path = issue.path.join('.');
                if (path) {
                    newErrors[path] = issue.message;
                }
            });
            errors = newErrors;
            return false;
        }
    }

    function handleKeydown(event: KeyboardEvent) {
        if (event.key === "Enter") {
            const form = event.target?.closest('form') as HTMLFormElement;
            if (form) {
                form.requestSubmit();
            }
        }
    }
</script>

<SEO 
    title={$t("auth.resetPasswordTitle")}
    description={$t("auth.resetPasswordDescription")}
    keywords="ตั้งรหัสผ่านใหม่, รีเซ็ตรหัสผ่าน, เปลี่ยนรหัสผ่าน"
    url="/reset-password"
    noindex={true}
/>

<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
    <Card class="w-full max-w-md p-8 space-y-6">
        <!-- Header -->
        <div class="text-center space-y-2">
            <div class="flex justify-center mb-4">
                <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                    <Icon icon="solar:key-bold" class="w-6 h-6 text-primary-foreground" />
                </div>
            </div>
            <h1 class="text-2xl font-bold text-foreground">{$t("auth.resetPasswordTitle")}</h1>
            <p class="text-muted-foreground">{$t("auth.resetPasswordDescription")}</p>
        </div>

        <!-- Form Action Messages -->
        {#if formResult?.success}
            <div class="alert alert-success">
                <Icon icon="mdi:check-circle" class="w-5 h-5" />
                <span>{formResult.message}</span>
            </div>
        {:else if formResult?.error}
            <div class="alert alert-error">
                <Icon icon="mdi:alert-circle" class="w-5 h-5" />
                <span>{formResult.error}</span>
            </div>
        {/if}

        <form 
            method="POST" 
            action="?/resetPassword"
            use:enhance={() => {
                isLoading = true;
                showLoading('กำลังตั้งรหัสผ่านใหม่...');
                
                return async ({ result }) => {
                    isLoading = false;
                    if (result.type === 'failure') {
                        showError("เกิดข้อผิดพลาด", result.data?.error || 'ไม่สามารถรีเซ็ตรหัสผ่านได้');
                    }
                };
            }}
            class="space-y-4"
        >
            <div class="space-y-2">
                <Input
                    id="password"
                    name="password"
                    type="password"
                    placeholder="••••••••"
                    bind:value={password}
                    label={$t("auth.newPassword")}
                    icon="mdi:lock"
                    error={errors.password}
                    required
                    showPasswordToggle
                    autocomplete="new-password"
                    onkeydown={handleKeydown}
                />
            </div>
            
            <div class="space-y-2">
                <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    placeholder="••••••••"
                    bind:value={confirmPassword}
                    label={$t("auth.confirmNewPassword")}
                    icon="mdi:lock-check"
                    error={errors.confirmPassword}
                    required
                    showPasswordToggle
                    autocomplete="new-password"
                    onkeydown={handleKeydown}
                />
            </div>
            
            <Button
                type="submit"
                disabled={isLoading}
                loading={isLoading}
                color="primary"
                size="lg"
                block
            >
                {#if !isLoading}
                    <Icon icon="solar:key-line-duotone" class="w-4 h-4 mr-2" />
                {/if}
                {isLoading ? $t("auth.resettingPassword") : $t("auth.resetPassword")}
            </Button>
        </form>
        
        <div class="relative">
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-border"></div>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-card text-muted-foreground">{$t("auth.or")}</span>
            </div>
        </div>
        
        <div class="text-center">
            <a 
                href="/signin" 
                class="text-primary hover:text-primary/80 font-medium transition-colors"
            >
                {$t("auth.backToSignin")}
            </a>
        </div>
    </Card>
</div> 