import { error, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { categoryService } from '$lib/services/category';
import type { Category } from '$lib/types';
import { requireAuth } from '$lib/utils/server-auth';

export const load: PageServerLoad = async ({ locals, params }) => {
    // ไม่ต้องตรวจสอบ auth อีก เพราะ layout จัดการแล้ว
    const { siteId } = params;

    try {
        const response = await categoryService.getCategories(siteId, locals.token!);

        if (!response.success) {
            throw error(500, response.error || 'Failed to load categories');
        }

        return {
            categories: response.data || []
        };
    } catch (err) {
        console.error('Error loading categories:', err);
        throw error(500, 'Failed to load categories');
    }
};

export const actions: Actions = {
    /**
     * ✅ Create Category - Hybrid Approach
     * Route API + Service Pattern
     */
    createCategory: async ({ request, locals, params }) => {
        try {
            const { siteId } = params;
            const formData = await request.formData();

            // Extract and validate form data at route level
            const categoryData = {
                target: (formData.get('target') as string || 'product') as 'product' | 'category' | 'page' | 'brand' | 'blog' | 'news',
                name: formData.get('name') as string,
                cover: formData.get('cover') as string || undefined,
                parentId: formData.get('parentId') as string || undefined,
                siteId
            };

            // Basic validation at route level
            if (!categoryData.name?.trim()) {
                return fail(400, {
                    message: 'กรุณากรอกชื่อหมวดหมู่',
                    type: 'create'
                });
            }

            if (categoryData.name.length > 100) {
                return fail(400, {
                    message: 'ชื่อหมวดหมู่ต้องไม่เกิน 100 ตัวอักษร',
                    type: 'create'
                });
            }

            // Call service for business logic + backend API
            const response = await categoryService.createCategory(categoryData, siteId, locals.token!);

            if (!response.success) {
                return fail(400, {
                    message: response.error,
                    type: 'create'
                });
            }

            return {
                success: true,
                data: response.data,
                message: 'สร้างหมวดหมู่สำเร็จ',
                type: 'create'
            };

        } catch (error) {
            console.error('Create category error:', error);
            return fail(500, {
                message: 'เกิดข้อผิดพลาดในการสร้างหมวดหมู่',
                type: 'create'
            });
        }
    },

    /**
     * ✅ Update Category - Hybrid Approach
     * Route API + Service Pattern
     */
    updateCategory: async ({ request, locals, params }) => {
        try {
            const { siteId } = params;
            const formData = await request.formData();
            const categoryId = formData.get('categoryId') as string;

            // Basic validation at route level
            if (!categoryId?.trim()) {
                return fail(400, {
                    message: 'ไม่พบ ID หมวดหมู่',
                    type: 'update'
                });
            }

            const categoryData = {
                target: (formData.get('target') as string || 'product') as 'product' | 'category' | 'page' | 'brand' | 'blog' | 'news',
                name: formData.get('name') as string,
                cover: formData.get('cover') as string || undefined,
                parentId: formData.get('parentId') as string || undefined
            };

            if (!categoryData.name?.trim()) {
                return fail(400, {
                    message: 'กรุณากรอกชื่อหมวดหมู่',
                    type: 'update'
                });
            }

            // Call service for business logic + backend API
            const response = await categoryService.updateCategory(categoryId, categoryData, siteId, locals.token!);

            if (!response.success) {
                return fail(400, {
                    message: response.error,
                    type: 'update'
                });
            }

            return {
                success: true,
                data: response.data,
                message: 'อัปเดตหมวดหมู่สำเร็จ',
                type: 'update'
            };

        } catch (error) {
            console.error('Update category error:', error);
            return fail(500, {
                message: 'เกิดข้อผิดพลาดในการอัปเดตหมวดหมู่',
                type: 'update'
            });
        }
    },

    /**
     * ✅ Delete Category - Hybrid Approach
     * Route API + Service Pattern
     */
    deleteCategory: async ({ request, locals, params }) => {
        try {
            const { siteId } = params;
            const formData = await request.formData();
            const categoryId = formData.get('categoryId') as string;

            // Basic validation at route level
            if (!categoryId?.trim()) {
                return fail(400, {
                    message: 'ไม่พบ ID หมวดหมู่',
                    type: 'delete'
                });
            }

            // Call service for business logic + backend API
            const response = await categoryService.deleteCategory(categoryId, siteId, locals.token!);

            if (!response.success) {
                return fail(400, {
                    message: response.error,
                    type: 'delete'
                });
            }

            return {
                success: true,
                message: 'ลบหมวดหมู่สำเร็จ',
                type: 'delete'
            };

        } catch (error) {
            console.error('Delete category error:', error);
            return fail(500, {
                message: 'เกิดข้อผิดพลาดในการลบหมวดหมู่',
                type: 'delete'
            });
        }
    }
}; 