<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { apiClient } from '$lib/api/client';
	import { showSuccessToast, showErrorToast } from '$lib/utils/sweetalert';
	import { Button } from '$lib/components/ui';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui';
	import { Badge } from '$lib/components/ui';
	import { Input } from '$lib/components/ui';
	import { Select } from '$lib/components/ui';
	import { Switch } from '$lib/components/ui';
	import { Textarea } from '$lib/components/ui';
	import Icon from '@iconify/svelte';

	interface SiteSettings {
		_id: string;
		siteName: string;
		siteDescription?: string;
		siteUrl?: string;
		logo?: string;
		favicon?: string;
		theme: {
			primaryColor: string;
			secondaryColor: string;
			fontFamily: string;
		};
		seo: {
			metaTitle?: string;
			metaDescription?: string;
			keywords?: string[];
			ogImage?: string;
		};
		contact: {
			email?: string;
			phone?: string;
			address?: string;
		};
		social: {
			facebook?: string;
			instagram?: string;
			twitter?: string;
			line?: string;
		};
		features: {
			enableCart: boolean;
			enableReviews: boolean;
			enableWishlist: boolean;
			enableChat: boolean;
		};
		notifications: {
			emailNotifications: boolean;
			smsNotifications: boolean;
			pushNotifications: boolean;
		};
		security: {
			twoFactorAuth: boolean;
			passwordPolicy: string;
			sessionTimeout: number;
		};
		updatedAt: string;
	}

	interface PaymentGateway {
		_id: string;
		name: string;
		provider: 'stripe' | 'omise' | 'promptpay' | 'truemoney';
		status: 'active' | 'inactive';
		config: {
			publicKey?: string;
			secretKey?: string;
			webhookUrl?: string;
		};
		fees: {
			percentage: number;
			fixed: number;
		};
		createdAt: string;
	}

	let settings: SiteSettings | null = $state(null);
	let paymentGateways: PaymentGateway[] = $state([]);
	let loading = $state(true);
	let saving = $state(false);
	let activeTab = $state('general');
	let showSecretKeys = $state(false);

	// ฟอร์มการตั้งค่า
	let settingsForm = $state({
		siteName: '',
		siteDescription: '',
		siteUrl: '',
		primaryColor: '#3b82f6',
		secondaryColor: '#64748b',
		fontFamily: 'Inter',
		metaTitle: '',
		metaDescription: '',
		keywords: '',
		email: '',
		phone: '',
		address: '',
		facebook: '',
		instagram: '',
		twitter: '',
		line: '',
		enableCart: true,
		enableReviews: true,
		enableWishlist: true,
		enableChat: false,
		emailNotifications: true,
		smsNotifications: false,
		pushNotifications: true,
		twoFactorAuth: false,
		passwordPolicy: 'medium',
		sessionTimeout: 30
	});

	// ฟอร์ม Payment Gateway
	let paymentForm = $state({
		name: '',
		provider: 'stripe' as PaymentGateway['provider'],
		publicKey: '',
		secretKey: '',
		webhookUrl: '',
		percentage: 0,
		fixed: 0
	});

	const siteId = $derived(page.params.siteId);

	// ดึงข้อมูลการตั้งค่า
	async function fetchSettings() {
		try {
			loading = true;
			const response = await apiClient.get(`/site/${siteId}/settings`);

			if (response.success) {
				settings = response.data.settings;
				if (settings) {
					// อัปเดตฟอร์ม
					settingsForm = {
						siteName: settings.siteName,
						siteDescription: settings.siteDescription || '',
						siteUrl: settings.siteUrl || '',
						primaryColor: settings.theme.primaryColor,
						secondaryColor: settings.theme.secondaryColor,
						fontFamily: settings.theme.fontFamily,
						metaTitle: settings.seo.metaTitle || '',
						metaDescription: settings.seo.metaDescription || '',
						keywords: settings.seo.keywords?.join(', ') || '',
						email: settings.contact.email || '',
						phone: settings.contact.phone || '',
						address: settings.contact.address || '',
						facebook: settings.social.facebook || '',
						instagram: settings.social.instagram || '',
						twitter: settings.social.twitter || '',
						line: settings.social.line || '',
						enableCart: settings.features.enableCart,
						enableReviews: settings.features.enableReviews,
						enableWishlist: settings.features.enableWishlist,
						enableChat: settings.features.enableChat,
						emailNotifications: settings.notifications.emailNotifications,
						smsNotifications: settings.notifications.smsNotifications,
						pushNotifications: settings.notifications.pushNotifications,
						twoFactorAuth: settings.security.twoFactorAuth,
						passwordPolicy: settings.security.passwordPolicy,
						sessionTimeout: settings.security.sessionTimeout
					};
				}
			} else {
				showErrorToast(response.message || 'ไม่สามารถดึงข้อมูลการตั้งค่าได้');
			}
		} catch (error: any) {
			console.error('Error fetching settings:', error);
			showErrorToast('เกิดข้อผิดพลาดในการดึงข้อมูลการตั้งค่า');
		} finally {
			loading = false;
		}
	}

	// ดึงข้อมูล Payment Gateways
	async function fetchPaymentGateways() {
		try {
			const response = await apiClient.get(`/payment/${siteId}/gateways`);

			if (response.success) {
				paymentGateways = response.data.gateways || [];
			} else {
				showErrorToast(response.message || 'ไม่สามารถดึงข้อมูล Payment Gateway ได้');
			}
		} catch (error: any) {
			console.error('Error fetching payment gateways:', error);
			showErrorToast('เกิดข้อผิดพลาดในการดึงข้อมูล Payment Gateway');
		}
	}

	// บันทึกการตั้งค่า
	async function saveSettings() {
		try {
			saving = true;

			const updateData = {
				siteName: settingsForm.siteName,
				siteDescription: settingsForm.siteDescription,
				siteUrl: settingsForm.siteUrl,
				theme: {
					primaryColor: settingsForm.primaryColor,
					secondaryColor: settingsForm.secondaryColor,
					fontFamily: settingsForm.fontFamily
				},
				seo: {
					metaTitle: settingsForm.metaTitle,
					metaDescription: settingsForm.metaDescription,
					keywords: settingsForm.keywords.split(',').map(k => k.trim()).filter(k => k)
				},
				contact: {
					email: settingsForm.email,
					phone: settingsForm.phone,
					address: settingsForm.address
				},
				social: {
					facebook: settingsForm.facebook,
					instagram: settingsForm.instagram,
					twitter: settingsForm.twitter,
					line: settingsForm.line
				},
				features: {
					enableCart: settingsForm.enableCart,
					enableReviews: settingsForm.enableReviews,
					enableWishlist: settingsForm.enableWishlist,
					enableChat: settingsForm.enableChat
				},
				notifications: {
					emailNotifications: settingsForm.emailNotifications,
					smsNotifications: settingsForm.smsNotifications,
					pushNotifications: settingsForm.pushNotifications
				},
				security: {
					twoFactorAuth: settingsForm.twoFactorAuth,
					passwordPolicy: settingsForm.passwordPolicy,
					sessionTimeout: settingsForm.sessionTimeout
				}
			};

			const response = await apiClient.put(`/site/${siteId}/settings`, updateData);

			if (response.success) {
				showSuccessToast('บันทึกการตั้งค่าเรียบร้อยแล้ว');
				await fetchSettings();
			} else {
				showErrorToast(response.message || 'ไม่สามารถบันทึกการตั้งค่าได้');
			}
		} catch (error: any) {
			console.error('Error saving settings:', error);
			showErrorToast('เกิดข้อผิดพลาดในการบันทึกการตั้งค่า');
		} finally {
			saving = false;
		}
	}

	// เพิ่ม Payment Gateway
	async function addPaymentGateway() {
		if (!paymentForm.name.trim()) {
			showErrorToast('กรุณากรอกชื่อ Payment Gateway');
			return;
		}

		try {
			const response = await apiClient.post(`/payment/${siteId}/gateways`, {
				name: paymentForm.name,
				provider: paymentForm.provider,
				config: {
					publicKey: paymentForm.publicKey,
					secretKey: paymentForm.secretKey,
					webhookUrl: paymentForm.webhookUrl
				},
				fees: {
					percentage: paymentForm.percentage,
					fixed: paymentForm.fixed
				}
			});

			if (response.success) {
				showSuccessToast('เพิ่ม Payment Gateway เรียบร้อยแล้ว');
				resetPaymentForm();
				await fetchPaymentGateways();
			} else {
				showErrorToast(response.message || 'ไม่สามารถเพิ่ม Payment Gateway ได้');
			}
		} catch (error: any) {
			console.error('Error adding payment gateway:', error);
			showErrorToast('เกิดข้อผิดพลาดในการเพิ่ม Payment Gateway');
		}
	}

	// ลบ Payment Gateway
	async function deletePaymentGateway(gatewayId: string) {
		if (!confirm('คุณแน่ใจหรือไม่ที่จะลบ Payment Gateway นี้?')) return;

		try {
			const response = await apiClient.delete(`/payment/${siteId}/gateways/${gatewayId}`);

			if (response.success) {
				showSuccessToast('ลบ Payment Gateway เรียบร้อยแล้ว');
				await fetchPaymentGateways();
			} else {
				showErrorToast(response.message || 'ไม่สามารถลบ Payment Gateway ได้');
			}
		} catch (error: any) {
			console.error('Error deleting payment gateway:', error);
			showErrorToast('เกิดข้อผิดพลาดในการลบ Payment Gateway');
		}
	}

	// รีเซ็ตฟอร์ม Payment
	function resetPaymentForm() {
		paymentForm = {
			name: '',
			provider: 'stripe',
			publicKey: '',
			secretKey: '',
			webhookUrl: '',
			percentage: 0,
			fixed: 0
		};
	}

	// ฟังก์ชันสำหรับแปลงชื่อ Provider
	function getProviderName(provider: string) {
		switch (provider) {
			case 'stripe': return 'Stripe';
			case 'omise': return 'Omise';
			case 'promptpay': return 'PromptPay';
			case 'truemoney': return 'TrueMoney';
			default: return provider;
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	onMount(() => {
		fetchSettings();
		fetchPaymentGateways();
	});
</script>

<svelte:head>
	<title>การตั้งค่า - Dashboard</title>
</svelte:head>

<div class="container mx-auto space-y-6 sm:space-y-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">การตั้งค่า</h1>
			<p class="text-muted-foreground">จัดการการตั้งค่าเว็บไซต์และระบบ</p>
		</div>
		<Button onclick={saveSettings} disabled={saving}>
			{#if saving}
				<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
			{:else}
				<Save class="h-4 w-4 mr-2" />
			{/if}
			บันทึกการตั้งค่า
		</Button>
	</div>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else}
		<!-- แท็บ -->
		<div class="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
			<Button
				variant={activeTab === 'general' ? 'default' : 'ghost'}
				size="sm"
				onclick={() => activeTab = 'general'}
			>
				<Globe class="h-4 w-4 mr-2" />
				ทั่วไป
			</Button>
			<Button
				variant={activeTab === 'payment' ? 'default' : 'ghost'}
				size="sm"
				onclick={() => activeTab = 'payment'}
			>
				<CreditCard class="h-4 w-4 mr-2" />
				การชำระเงิน
			</Button>
			<Button
				variant={activeTab === 'security' ? 'default' : 'ghost'}
				size="sm"
				onclick={() => activeTab = 'security'}
			>
				<Shield class="h-4 w-4 mr-2" />
				ความปลอดภัย
			</Button>
			<Button
				variant={activeTab === 'notifications' ? 'default' : 'ghost'}
				size="sm"
				onclick={() => activeTab = 'notifications'}
			>
				<Bell class="h-4 w-4 mr-2" />
				การแจ้งเตือน
			</Button>
		</div>

		{#if activeTab === 'general'}
			<div class="grid gap-6">
				<!-- ข้อมูลเว็บไซต์ -->
				<Card>
					<CardHeader>
						<CardTitle>ข้อมูลเว็บไซต์</CardTitle>
						<CardDescription>ข้อมูลพื้นฐานของเว็บไซต์</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label class="text-sm font-medium">ชื่อเว็บไซต์</label>
								<Input bind:value={settingsForm.siteName} />
							</div>
							<div class="space-y-2">
								<label class="text-sm font-medium">URL เว็บไซต์</label>
								<Input bind:value={settingsForm.siteUrl} placeholder="https://example.com" />
							</div>
						</div>
						<div class="space-y-2">
							<label class="text-sm font-medium">คำอธิบายเว็บไซต์</label>
							<Textarea bind:value={settingsForm.siteDescription} rows={3} />
						</div>
					</CardContent>
				</Card>

				<!-- ธีม -->
				<Card>
					<CardHeader>
						<CardTitle>ธีมและการแสดงผล</CardTitle>
						<CardDescription>ปรับแต่งสีและฟอนต์</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="grid grid-cols-3 gap-4">
							<div class="space-y-2">
								<label class="text-sm font-medium">สีหลัก</label>
								<Input type="color" bind:value={settingsForm.primaryColor} />
							</div>
							<div class="space-y-2">
								<label class="text-sm font-medium">สีรอง</label>
								<Input type="color" bind:value={settingsForm.secondaryColor} />
							</div>
							<div class="space-y-2">
								<label class="text-sm font-medium">ฟอนต์</label>
								<Select 
									bind:value={settingsForm.fontFamily}
									options={[
										{ value: "Inter", label: "Inter" },
										{ value: "Roboto", label: "Roboto" },
										{ value: "Open Sans", label: "Open Sans" },
										{ value: "Lato", label: "Lato" }
									]}
								/>
							</div>
						</div>
					</CardContent>
				</Card>

				<!-- SEO -->
				<Card>
					<CardHeader>
						<CardTitle>SEO</CardTitle>
						<CardDescription>การตั้งค่าสำหรับเครื่องมือค้นหา</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label class="text-sm font-medium">Meta Title</label>
								<Input bind:value={settingsForm.metaTitle} />
							</div>
							<div class="space-y-2">
								<label class="text-sm font-medium">Keywords (คั่นด้วยจุลภาค)</label>
								<Input bind:value={settingsForm.keywords} placeholder="keyword1, keyword2, keyword3" />
							</div>
						</div>
						<div class="space-y-2">
							<label class="text-sm font-medium">Meta Description</label>
							<Textarea bind:value={settingsForm.metaDescription} rows={3} />
						</div>
					</CardContent>
				</Card>

				<!-- ข้อมูลติดต่อ -->
				<Card>
					<CardHeader>
						<CardTitle>ข้อมูลติดต่อ</CardTitle>
						<CardDescription>ข้อมูลการติดต่อและโซเชียลมีเดีย</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label class="text-sm font-medium">อีเมล</label>
								<Input type="email" bind:value={settingsForm.email} />
							</div>
							<div class="space-y-2">
								<label class="text-sm font-medium">เบอร์โทรศัพท์</label>
								<Input bind:value={settingsForm.phone} />
							</div>
						</div>
						<div class="space-y-2">
							<label class="text-sm font-medium">ที่อยู่</label>
							<Textarea bind:value={settingsForm.address} rows={2} />
						</div>
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label class="text-sm font-medium">Facebook</label>
								<Input bind:value={settingsForm.facebook} placeholder="https://facebook.com/..." />
							</div>
							<div class="space-y-2">
								<label class="text-sm font-medium">Instagram</label>
								<Input bind:value={settingsForm.instagram} placeholder="https://instagram.com/..." />
							</div>
							<div class="space-y-2">
								<label class="text-sm font-medium">Twitter</label>
								<Input bind:value={settingsForm.twitter} placeholder="https://twitter.com/..." />
							</div>
							<div class="space-y-2">
								<label class="text-sm font-medium">Line</label>
								<Input bind:value={settingsForm.line} placeholder="@lineid" />
							</div>
						</div>
					</CardContent>
				</Card>

				<!-- ฟีเจอร์ -->
				<Card>
					<CardHeader>
						<CardTitle>ฟีเจอร์เว็บไซต์</CardTitle>
						<CardDescription>เปิด/ปิดฟีเจอร์ต่างๆ</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="grid grid-cols-2 gap-4">
							<div class="flex items-center justify-between">
								<label class="text-sm font-medium">ตะกร้าสินค้า</label>
								<Switch bind:checked={settingsForm.enableCart} />
							</div>
							<div class="flex items-center justify-between">
								<label class="text-sm font-medium">รีวิวสินค้า</label>
								<Switch bind:checked={settingsForm.enableReviews} />
							</div>
							<div class="flex items-center justify-between">
								<label class="text-sm font-medium">รายการโปรด</label>
								<Switch bind:checked={settingsForm.enableWishlist} />
							</div>
							<div class="flex items-center justify-between">
								<label class="text-sm font-medium">แชทสด</label>
								<Switch bind:checked={settingsForm.enableChat} />
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

		{:else if activeTab === 'payment'}
			<div class="grid gap-6">
				<!-- เพิ่ม Payment Gateway -->
				<Card>
					<CardHeader>
						<CardTitle>เพิ่ม Payment Gateway</CardTitle>
						<CardDescription>เพิ่มช่องทางการชำระเงินใหม่</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label class="text-sm font-medium">ชื่อ</label>
								<Input bind:value={paymentForm.name} placeholder="Stripe Payment" />
							</div>
							<div class="space-y-2">
								<label class="text-sm font-medium">Provider</label>
								<Select 
									bind:value={paymentForm.provider}
									options={[
										{ value: "stripe", label: "Stripe" },
										{ value: "omise", label: "Omise" },
										{ value: "promptpay", label: "PromptPay" },
										{ value: "truemoney", label: "TrueMoney" }
									]}
								/>
							</div>
						</div>
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label class="text-sm font-medium">Public Key</label>
								<Input bind:value={paymentForm.publicKey} />
							</div>
							<div class="space-y-2">
								<label class="text-sm font-medium">Secret Key</label>
								<div class="relative">
									<Input
										type={showSecretKeys ? 'text' : 'password'}
										bind:value={paymentForm.secretKey}
									/>
									<Button
										variant="ghost"
										size="sm"
										class="absolute right-2 top-1/2 transform -translate-y-1/2"
										onclick={() => showSecretKeys = !showSecretKeys}
									>
										{#if showSecretKeys}
											<EyeOff class="h-4 w-4" />
										{:else}
											<Eye class="h-4 w-4" />
										{/if}
									</Button>
								</div>
							</div>
						</div>
						<div class="space-y-2">
							<label class="text-sm font-medium">Webhook URL</label>
							<Input bind:value={paymentForm.webhookUrl} placeholder="https://..." />
						</div>
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label class="text-sm font-medium">ค่าธรรมเนียม (%)</label>
								<Input type="number" step="0.01" bind:value={paymentForm.percentage} />
							</div>
							<div class="space-y-2">
								<label class="text-sm font-medium">ค่าธรรมเนียมคงที่ (บาท)</label>
								<Input type="number" step="0.01" bind:value={paymentForm.fixed} />
							</div>
						</div>
						<Button onclick={addPaymentGateway}>
							<CreditCard class="h-4 w-4 mr-2" />
							เพิ่ม Payment Gateway
						</Button>
					</CardContent>
				</Card>

				<!-- รายการ Payment Gateways -->
				<Card>
					<CardHeader>
						<CardTitle>Payment Gateways</CardTitle>
						<CardDescription>ช่องทางการชำระเงินที่ใช้งานอยู่</CardDescription>
					</CardHeader>
					<CardContent>
						{#if paymentGateways.length === 0}
							<div class="text-center py-8 text-muted-foreground">
								ยังไม่มี Payment Gateway
							</div>
						{:else}
							<div class="space-y-4">
								{#each paymentGateways as gateway (gateway._id)}
									<div class="flex items-center justify-between p-4 border rounded-lg">
										<div class="flex items-center gap-4">
											<CreditCard class="h-8 w-8 text-muted-foreground" />
											<div>
												<h3 class="font-medium">{gateway.name}</h3>
												<p class="text-sm text-muted-foreground">
													{getProviderName(gateway.provider)} •
													ค่าธรรมเนียม {gateway.fees.percentage}% + {gateway.fees.fixed} บาท
												</p>
												<p class="text-xs text-muted-foreground">
													เพิ่มเมื่อ {formatDate(gateway.createdAt)}
												</p>
											</div>
										</div>
										<div class="flex items-center gap-2">
											<Badge variant={gateway.status === 'active' ? 'default' : 'secondary'}>
												{gateway.status === 'active' ? 'ใช้งาน' : 'ไม่ใช้งาน'}
											</Badge>
											<Button
												variant="ghost"
												size="sm"
												onclick={() => deletePaymentGateway(gateway._id)}
											>
												ลบ
											</Button>
										</div>
									</div>
								{/each}
							</div>
						{/if}
					</CardContent>
				</Card>
			</div>

		{:else if activeTab === 'security'}
			<div class="grid gap-6">
				<Card>
					<CardHeader>
						<CardTitle>ความปลอดภัย</CardTitle>
						<CardDescription>การตั้งค่าความปลอดภัยของระบบ</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="flex items-center justify-between">
							<div>
								<label class="text-sm font-medium">Two-Factor Authentication</label>
								<p class="text-xs text-muted-foreground">เพิ่มความปลอดภัยด้วยการยืนยันตัวตน 2 ขั้นตอน</p>
							</div>
							<Switch bind:checked={settingsForm.twoFactorAuth} />
						</div>

						<div class="space-y-2">
							<label class="text-sm font-medium">นโยบายรหัสผ่าน</label>
							<Select 
								bind:value={settingsForm.passwordPolicy}
								options={[
									{ value: "weak", label: "อ่อน (6 ตัวอักษร)" },
									{ value: "medium", label: "ปานกลาง (8 ตัวอักษร + ตัวเลข)" },
									{ value: "strong", label: "แข็งแกร่ง (8 ตัวอักษร + ตัวเลข + สัญลักษณ์)" }
								]}
							/>
						</div>

						<div class="space-y-2">
							<label class="text-sm font-medium">Session Timeout (นาที)</label>
							<Input type="number" bind:value={settingsForm.sessionTimeout} />
						</div>
					</CardContent>
				</Card>
			</div>

		{:else if activeTab === 'notifications'}
			<div class="grid gap-6">
				<Card>
					<CardHeader>
						<CardTitle>การแจ้งเตือน</CardTitle>
						<CardDescription>การตั้งค่าการแจ้งเตือนต่างๆ</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="flex items-center justify-between">
							<div>
								<label class="text-sm font-medium">การแจ้งเตือนทางอีเมล</label>
								<p class="text-xs text-muted-foreground">รับการแจ้งเตือนผ่านอีเมล</p>
							</div>
							<Switch bind:checked={settingsForm.emailNotifications} />
						</div>

						<div class="flex items-center justify-between">
							<div>
								<label class="text-sm font-medium">การแจ้งเตือนทาง SMS</label>
								<p class="text-xs text-muted-foreground">รับการแจ้งเตือนผ่าง SMS</p>
							</div>
							<Switch bind:checked={settingsForm.smsNotifications} />
						</div>

						<div class="flex items-center justify-between">
							<div>
								<label class="text-sm font-medium">Push Notifications</label>
								<p class="text-xs text-muted-foreground">รับการแจ้งเตือนแบบ Push</p>
							</div>
							<Switch bind:checked={settingsForm.pushNotifications} />
						</div>
					</CardContent>
				</Card>
			</div>
		{/if}
	{/if}
</div>