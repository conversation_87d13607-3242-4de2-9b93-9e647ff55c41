<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { apiClient } from '$lib/api/client';
	import { showSuccessToast, showErrorToast } from '$lib/utils/sweetalert';

	import { Card } from '$lib/components/ui';
	import { Button,Badge,Input } from '$lib/components/ui'; 
	import { Select } from '$lib/components/ui';
	import { Dialog } from '$lib/components/ui';
	import { Table } from '$lib/components/ui';
	import Icon from '@iconify/svelte';

	interface ContentItem {
		_id: string;
		title: string;
		type: 'page' | 'post' | 'media' | 'menu';
		status: 'published' | 'draft' | 'archived';
		slug?: string;
		content?: string;
		excerpt?: string;
		featuredImage?: string;
		author: string;
		publishedAt?: string;
		createdAt: string;
		updatedAt: string;
	}

	let contentItems: ContentItem[] = $state([]);
	let loading = $state(true);
	let searchQuery = $state('');
	let typeFilter = $state('all');
	let statusFilter = $state('all');
	let selectedContent: ContentItem | null = $state(null);
	let createDialogOpen = $state(false);
	let viewDialogOpen = $state(false);
	let processing = $state(false);

	// ฟอร์มสร้างเนื้อหาใหม่
	let createForm = $state({
		title: '',
		type: 'page' as ContentItem['type'],
		status: 'draft' as ContentItem['status'],
		slug: '',
		content: '',
		excerpt: ''
	});

	const siteId = $derived(page.params.siteId);
	const filteredContent = $derived(contentItems.filter(item => {
		const matchesSearch = 
			item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
			(item.slug && item.slug.toLowerCase().includes(searchQuery.toLowerCase()));

		const matchesType = 
			typeFilter === 'all' || item.type === typeFilter;

		const matchesStatus = 
			statusFilter === 'all' || item.status === statusFilter;

		return matchesSearch && matchesType && matchesStatus;
	}));

	// ดึงข้อมูลเนื้อหา
	async function fetchContent() {
		try {
			loading = true;
			const response = await apiClient.get(`/content/${siteId}`);
			
			if (response.success) {
				contentItems = response.data.content || [];
			} else {
				showErrorToast(response.message || 'ไม่สามารถดึงข้อมูลเนื้อหาได้');
			}
		} catch (error: any) {
			console.error('Error fetching content:', error);
			showErrorToast('เกิดข้อผิดพลาดในการดึงข้อมูลเนื้อหา');
		} finally {
			loading = false;
		}
	}

	// สร้างเนื้อหาใหม่
	async function createContent() {
		if (!createForm.title.trim()) {
			showErrorToast('กรุณากรอกชื่อเรื่อง');
			return;
		}

		try {
			processing = true;
			const response = await apiClient.post(`/content/${siteId}`, createForm);
			
			if (response.success) {
				showSuccessToast('สร้างเนื้อหาเรียบร้อยแล้ว');
				createDialogOpen = false;
				resetCreateForm();
				await fetchContent();
			} else {
				showErrorToast(response.message || 'ไม่สามารถสร้างเนื้อหาได้');
			}
		} catch (error: any) {
			console.error('Error creating content:', error);
			showErrorToast('เกิดข้อผิดพลาดในการสร้างเนื้อหา');
		} finally {
			processing = false;
		}
	}

	// ลบเนื้อหา
	async function deleteContent(contentId: string) {
		if (!confirm('คุณแน่ใจหรือไม่ที่จะลบเนื้อหานี้?')) return;

		try {
			const response = await apiClient.delete(`/content/${siteId}/${contentId}`);
			
			if (response.success) {
				showSuccessToast('ลบเนื้อหาเรียบร้อยแล้ว');
				await fetchContent();
			} else {
				showErrorToast(response.message || 'ไม่สามารถลบเนื้อหาได้');
			}
		} catch (error: any) {
			console.error('Error deleting content:', error);
			showErrorToast('เกิดข้อผิดพลาดในการลบเนื้อหา');
		}
	}

	// อัปเดตสถานะเนื้อหา
	async function updateContentStatus(contentId: string, status: ContentItem['status']) {
		try {
			const response = await apiClient.put(`/content/${siteId}/${contentId}`, { status });
			
			if (response.success) {
				showSuccessToast('อัปเดตสถานะเรียบร้อยแล้ว');
				await fetchContent();
			} else {
				showErrorToast(response.message || 'ไม่สามารถอัปเดตสถานะได้');
			}
		} catch (error: any) {
			console.error('Error updating content status:', error);
			showErrorToast('เกิดข้อผิดพลาดในการอัปเดตสถานะ');
		}
	}

	// เปิดดูรายละเอียด
	function viewContent(content: ContentItem) {
		selectedContent = content;
		viewDialogOpen = true;
	}

	// รีเซ็ตฟอร์ม
	function resetCreateForm() {
		createForm = {
			title: '',
			type: 'page',
			status: 'draft',
			slug: '',
			content: '',
			excerpt: ''
		};
	}

	// สร้าง slug อัตโนมัติ
	function generateSlug(title: string) {
		return title
			.toLowerCase()
			.replace(/[^\w\s-]/g, '')
			.replace(/\s+/g, '-')
			.trim();
	}

	// อัปเดต slug เมื่อเปลี่ยนชื่อเรื่อง
	$effect(() => {
		if (createForm.title && !createForm.slug) {
			createForm.slug = generateSlug(createForm.title);
		}
	});

	// ฟังก์ชันสำหรับแสดงไอคอนตามประเภท
	function getTypeIcon(type: string) {
		switch (type) {
			case 'page': return Globe;
			case 'post': return FileText;
			case 'media': return Image;
			case 'menu': return File;
			default: return FileText;
		}
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตามสถานะ
	function getStatusBadgeVariant(status: string) {
		switch (status) {
			case 'published': return 'default';
			case 'draft': return 'secondary';
			case 'archived': return 'outline';
			default: return 'outline';
		}
	}

	// ฟังก์ชันสำหรับแปลงประเภทเป็นภาษาไทย
	function getTypeText(type: string) {
		switch (type) {
			case 'page': return 'หน้าเว็บ';
			case 'post': return 'บทความ';
			case 'media': return 'สื่อ';
			case 'menu': return 'เมนู';
			default: return type;
		}
	}

	// ฟังก์ชันสำหรับแปลงสถานะเป็นภาษาไทย
	function getStatusText(status: string) {
		switch (status) {
			case 'published': return 'เผยแพร่แล้ว';
			case 'draft': return 'ร่าง';
			case 'archived': return 'เก็บถาวร';
			default: return status;
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// คำนวณสถิติเนื้อหา
	const contentStats = $derived({
		total: contentItems.length,
		published: contentItems.filter(c => c.status === 'published').length,
		draft: contentItems.filter(c => c.status === 'draft').length,
		archived: contentItems.filter(c => c.status === 'archived').length,
		pages: contentItems.filter(c => c.type === 'page').length,
		posts: contentItems.filter(c => c.type === 'post').length
	});

	onMount(() => {
		fetchContent();
	});
</script>

<svelte:head>
	<title>จัดการเนื้อหา - Dashboard</title>
</svelte:head>

<div class="container mx-auto space-y-6 sm:space-y-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">จัดการเนื้อหา</h1>
			<p class="text-muted-foreground">จัดการหน้าเว็บ บทความ และสื่อต่างๆ</p>
		</div>
		<Button onclick={() => createDialogOpen = true}>
			<Plus class="h-4 w-4 mr-2" />
			สร้างเนื้อหาใหม่
		</Button>
	</div>

	<!-- สถิติเนื้อหา -->
	<div class="grid gap-4 md:grid-cols-6">
		<Card classBody="p-6">
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">ทั้งหมด</div>
				<Icon icon="mdi:file-document" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold">{contentStats.total}</div>
		</Card>
		<Card classBody="p-6">
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">เผยแพร่แล้ว</div>
				<Icon icon="mdi:globe" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold text-green-600">{contentStats.published}</div>
		</Card>
		<Card classBody="p-6">
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">ร่าง</div>
				<Icon icon="mdi:pencil" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold text-orange-600">{contentStats.draft}</div>
		</Card>
		<Card classBody="p-6">
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">เก็บถาวร</div>
				<Icon icon="mdi:file" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold text-gray-600">{contentStats.archived}</div>
		</Card>
		<Card classBody="p-6">
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">หน้าเว็บ</div>
				<Icon icon="mdi:globe" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold">{contentStats.pages}</div>
		</Card>
		<Card classBody="p-6">
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">บทความ</div>
				<Icon icon="mdi:file-document" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold">{contentStats.posts}</div>
		</Card>
	</div>

	<!-- ตัวกรองและค้นหา -->
	<Card classBody="p-4">
		<div class="flex flex-col md:flex-row gap-4">
			<div class="relative flex-1">
				<Icon icon="mdi:magnify" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
				<Input 
					placeholder="ค้นหาเนื้อหา..."
					bind:value={searchQuery}
					class="pl-10"
				/>
			</div>
			<Select 
				bind:value={typeFilter}
				placeholder="ประเภท"
				className="w-full md:w-48"
				options={[
					{ value: "all", label: "ทั้งหมด" },
					{ value: "page", label: "หน้าเว็บ" },
					{ value: "post", label: "บทความ" },
					{ value: "media", label: "สื่อ" },
					{ value: "menu", label: "เมนู" }
				]}
			/>
			<Select 
				bind:value={statusFilter}
				placeholder="สถานะ"
				className="w-full md:w-48"
				options={[
					{ value: "all", label: "ทั้งหมด" },
					{ value: "published", label: "เผยแพร่แล้ว" },
					{ value: "draft", label: "ร่าง" },
					{ value: "archived", label: "เก็บถาวร" }
				]}
			/>
		</div>
	</Card>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else}
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<FileText class="h-5 w-5" />
					เนื้อหาทั้งหมด ({filteredContent.length})
				</CardTitle>
			</CardHeader>
			<CardContent>
				{#if filteredContent.length === 0}
					<div class="text-center py-8 text-muted-foreground">
						{searchQuery || typeFilter !== 'all' || statusFilter !== 'all'
							? 'ไม่พบเนื้อหาที่ตรงกับเงื่อนไข' 
							: 'ยังไม่มีเนื้อหา'}
					</div>
				{:else}
					<Table 
						columns={[
							{ key: 'title', label: 'เนื้อหา', searchable: true },
							{ key: 'type', label: 'ประเภท', searchable: true },
							{ key: 'status', label: 'สถานะ', searchable: true },
							{ key: 'author', label: 'ผู้เขียน', searchable: true },
							{ key: 'createdAt', label: 'วันที่สร้าง' },
							{ key: 'publishedAt', label: 'วันที่เผยแพร่' },
							{ key: 'actions', label: 'การจัดการ', align: 'right' }
						]}
						data={filteredContent.map(content => ({
							...content,
							actions: `
								<div class="flex items-center justify-end gap-2">
									<button class="btn btn-ghost btn-sm" onclick="viewContent('${content._id}')">
										<Icon icon="mdi:eye" class="h-4 w-4" />
									</button>
									<button class="btn btn-ghost btn-sm" onclick="editContent('${content._id}')">
										<Icon icon="mdi:pencil" class="h-4 w-4" />
									</button>
									${content.status === 'draft' ? `
										<button class="btn btn-ghost btn-sm" onclick="publishContent('${content._id}')">
											เผยแพร่
										</button>
									` : content.status === 'published' ? `
										<button class="btn btn-ghost btn-sm" onclick="archiveContent('${content._id}')">
											เก็บถาวร
										</button>
									` : ''}
									<button class="btn btn-ghost btn-sm" onclick="deleteContent('${content._id}')">
										<Icon icon="mdi:trash" class="h-4 w-4" />
									</button>
								</div>
							`
						}))}
						searchable={true}
						pagination={true}
						pageSize={10}
						onRowClick={(row) => viewContent(row)}
					/>
				{/if}
			</CardContent>
		</Card>
	{/if}
</div>

<!-- Dialog สร้างเนื้อหาใหม่ -->
<Dialog 
	bind:open={createDialogOpen}
	title="สร้างเนื้อหาใหม่"
	iconTitle="mdi:plus"
	description="สร้างหน้าเว็บ บทความ หรือเนื้อหาใหม่"
	size="lg"
>
	<div class="space-y-4">
		<div class="space-y-2">
			<label class="text-sm font-medium">ชื่อเรื่อง</label>
			<Input 
				placeholder="ชื่อเรื่อง..."
				bind:value={createForm.title}
			/>
		</div>
		
		<div class="grid grid-cols-2 gap-4">
			<div class="space-y-2">
				<label class="text-sm font-medium">ประเภท</label>
				<Select 
					options={[
						{ value: 'page', label: 'หน้าเว็บ' },
						{ value: 'post', label: 'บทความ' },
						{ value: 'media', label: 'สื่อ' },
						{ value: 'menu', label: 'เมนู' }
					]}
					bind:value={createForm.type}
				/>
			</div>
			
			<div class="space-y-2">
				<label class="text-sm font-medium">สถานะ</label>
				<Select 
					options={[
						{ value: 'draft', label: 'ร่าง' },
						{ value: 'published', label: 'เผยแพร่' }
					]}
					bind:value={createForm.status}
				/>
			</div>
		</div>
		
		<div class="space-y-2">
			<label class="text-sm font-medium">Slug (URL)</label>
			<Input 
				placeholder="url-slug"
				bind:value={createForm.slug}
			/>
		</div>
		
		<div class="space-y-2">
			<label class="text-sm font-medium">คำอธิบายสั้น</label>
			<textarea 
				class="w-full p-2 border rounded-md"
				rows="2"
				placeholder="คำอธิบายสั้นๆ..."
				bind:value={createForm.excerpt}
			></textarea>
		</div>
		
		<div class="space-y-2">
			<label class="text-sm font-medium">เนื้อหา</label>
			<textarea 
				class="w-full p-2 border rounded-md"
				rows="6"
				placeholder="เนื้อหา..."
				bind:value={createForm.content}
			></textarea>
		</div>
		
		<div class="flex gap-2">
			<Button 
				onclick={createContent}
				disabled={processing}
				class="flex-1"
			>
				{#if processing}
					<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
				{:else}
					<Icon icon="mdi:plus" class="h-4 w-4 mr-2" />
				{/if}
				สร้างเนื้อหา
			</Button>
			<Button variant="outline" onclick={() => createDialogOpen = false}>
				ยกเลิก
			</Button>
		</div>
	</div>
</Dialog>

<!-- Dialog ดูรายละเอียด -->
<Dialog 
	bind:open={viewDialogOpen}
	title={selectedContent?.title || 'รายละเอียดเนื้อหา'}
	iconTitle="mdi:file-document"
	description={selectedContent ? `${getTypeText(selectedContent.type)} • ${getStatusText(selectedContent.status)}` : ''}
	size="4xl"
>
	{#if selectedContent}
		<div class="space-y-4">
			{#if selectedContent.excerpt}
				<div>
					<h3 class="font-semibold mb-2">คำอธิบายสั้น</h3>
					<p class="text-sm text-muted-foreground">{selectedContent.excerpt}</p>
				</div>
			{/if}
			
			{#if selectedContent.content}
				<div>
					<h3 class="font-semibold mb-2">เนื้อหา</h3>
					<div class="bg-muted p-4 rounded-lg text-sm whitespace-pre-wrap">
						{selectedContent.content}
					</div>
				</div>
			{/if}
			
			<div class="grid grid-cols-2 gap-4 text-sm">
				<div>
					<p class="font-medium">ผู้เขียน</p>
					<p>{selectedContent.author}</p>
				</div>
				<div>
					<p class="font-medium">วันที่สร้าง</p>
					<p>{formatDate(selectedContent.createdAt)}</p>
				</div>
				{#if selectedContent.publishedAt}
					<div>
						<p class="font-medium">วันที่เผยแพร่</p>
						<p>{formatDate(selectedContent.publishedAt)}</p>
					</div>
				{/if}
				<div>
					<p class="font-medium">อัปเดตล่าสุด</p>
					<p>{formatDate(selectedContent.updatedAt)}</p>
				</div>
			</div>
		</div>
	{/if}
</Dialog>
