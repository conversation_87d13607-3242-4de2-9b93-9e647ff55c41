<script lang="ts">
    import Icon from "@iconify/svelte";
    import { t } from "svelte-i18n";
    import { authStore } from "$lib/stores/auth.svelte";
    import { siteStore } from "$lib/stores/site.svelte";
    import Chart from "$lib/components/ui/Chart.svelte";
    import SEO from "$lib/components/layout/SEO.svelte";

    let { data } = $props<{
        data: { user?: any; site?: any; error?: string };
    }>();

    let user = $derived(data?.user || authStore.user);
    let error = $derived(data?.error);
    let site = $derived(data?.site);

    // รอให้ authStore initialized
    let isReady = $derived(authStore.isInitialized);

    // Mock data สำหรับ teams dashboard
    const teamData = {
        roles: {
            labels: ['Admin', 'Manager', 'Editor', 'Viewer'],
            datasets: [{
                data: [2, 3, 5, 8],
                backgroundColor: [
                    'rgb(239, 68, 68)',
                    'rgb(245, 158, 11)',
                    'rgb(59, 130, 246)',
                    'rgb(34, 197, 94)'
                ]
            }]
        },
        activity: {
            labels: ['จันทร์', 'อังคาร', 'พุธ', 'พฤหัส', 'ศุกร์', 'เสาร์', 'อาทิตย์'],
            datasets: [{
                label: 'กิจกรรมทีม',
                data: [12, 19, 15, 25, 22, 18, 14],
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            }]
        }
    };

    const chartOptions = {
        plugins: {
            legend: {
                display: true,
                position: 'top' as const
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    };

    const pieOptions = {
        plugins: {
            legend: {
                display: true,
                position: 'bottom' as const
            }
        }
    };

    // สถิติทีม
    const teamStats = [
        {
            title: 'สมาชิกทั้งหมด',
            value: '18',
            change: '+2',
            changeType: 'positive',
            icon: 'mdi:account-group',
            color: 'bg-blue-500'
        },
        {
            title: 'Admin',
            value: '2',
            change: '+0',
            changeType: 'neutral',
            icon: 'mdi:crown',
            color: 'bg-red-500'
        },
        {
            title: 'Manager',
            value: '3',
            change: '+1',
            changeType: 'positive',
            icon: 'mdi:account-tie',
            color: 'bg-yellow-500'
        },
        {
            title: 'Editor',
            value: '5',
            change: '+2',
            changeType: 'positive',
            icon: 'mdi:pencil',
            color: 'bg-green-500'
        }
    ];

    // สมาชิกทีม
    const teamMembers = [
        {
            name: 'สมชาย ใจดี',
            email: '<EMAIL>',
            role: 'admin',
            status: 'active',
            lastActive: '2 นาทีที่แล้ว',
            avatar: 'SC',
            permissions: ['manage_users', 'manage_content', 'view_analytics']
        },
        {
            name: 'สมหญิง สวยงาม',
            email: '<EMAIL>',
            role: 'manager',
            status: 'active',
            lastActive: '5 นาทีที่แล้ว',
            avatar: 'SS',
            permissions: ['manage_content', 'view_analytics']
        },
        {
            name: 'สมศักดิ์ เก่งกล้า',
            email: '<EMAIL>',
            role: 'editor',
            status: 'active',
            lastActive: '10 นาทีที่แล้ว',
            avatar: 'SK',
            permissions: ['edit_content']
        },
        {
            name: 'สมปอง รักดี',
            email: '<EMAIL>',
            role: 'viewer',
            status: 'inactive',
            lastActive: '2 ชั่วโมงที่แล้ว',
            avatar: 'SR',
            permissions: ['view_content']
        }
    ];

    function getRoleBadge(role: string) {
        const badges = {
            admin: 'badge-error',
            manager: 'badge-warning',
            editor: 'badge-info',
            viewer: 'badge-success'
        };
        return badges[role as keyof typeof badges] || 'badge-neutral';
    }

    function getRoleText(role: string) {
        const texts = {
            admin: 'Admin',
            manager: 'Manager',
            editor: 'Editor',
            viewer: 'Viewer'
        };
        return texts[role as keyof typeof texts] || role;
    }

    function getStatusBadge(status: string) {
        return status === 'active' ? 'badge-success' : 'badge-error';
    }

    function getStatusText(status: string) {
        return status === 'active' ? 'ใช้งาน' : 'ไม่ใช้งาน';
    }
</script>

<SEO
    title="Teams - จัดการทีม"
    description="จัดการสมาชิกทีม สิทธิ์ และการทำงานร่วมกัน"
    keywords="teams, ทีม, สมาชิก, สิทธิ์, การทำงานร่วมกัน"
    url="/dashboard/teams"
    noindex={true}
/>

{#if !isReady}
    <div class="flex items-center justify-center min-h-screen">
        <div class="loading loading-spinner loading-lg"></div>
    </div>
{:else}
    <div class="space-y-6 sm:space-y-6">
        <!-- Header -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-base-content">
                    <Icon icon="mdi:account-group" class="w-8 h-8 inline mr-2" />
                    จัดการทีม
                </h1>
                <p class="text-base-content/60 mt-1">
                    จัดการสมาชิกทีม สิทธิ์ และการทำงานร่วมกัน
                </p>
            </div>
            <div class="flex gap-2">
                <button class="btn btn-primary">
                    <Icon icon="mdi:account-plus" class="w-5 h-5" />
                    เพิ่มสมาชิก
                </button>
                <button class="btn btn-outline">
                    <Icon icon="mdi:download" class="w-5 h-5" />
                    ส่งออกข้อมูล
                </button>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {#each teamStats as stat}
                <div class="card bg-base-100 shadow-lg">
                    <div class="card-body">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-base-content/60">{stat.title}</p>
                                <p class="text-2xl font-bold text-base-content">{stat.value}</p>
                                <p class="text-sm {stat.changeType === 'positive' ? 'text-green-500' : stat.changeType === 'negative' ? 'text-red-500' : 'text-gray-500'}">
                                    {stat.change} จากเดือนที่แล้ว
                                </p>
                            </div>
                            <div class="w-12 h-12 rounded-full {stat.color} flex items-center justify-center">
                                <Icon icon={stat.icon} class="w-6 h-6 text-white" />
                            </div>
                        </div>
                    </div>
                </div>
            {/each}
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Team Activity -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title">
                        <Icon icon="mdi:chart-line" class="w-5 h-5" />
                        กิจกรรมทีมรายสัปดาห์
                    </h3>
                    <Chart data={teamData.activity} options={chartOptions} height="300px" />
                </div>
            </div>

            <!-- Team Roles -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title">
                        <Icon icon="mdi:pie-chart" class="w-5 h-5" />
                        สัดส่วนบทบาท
                    </h3>
                    <Chart type="doughnut" data={teamData.roles} options={pieOptions} height="300px" />
                </div>
            </div>
        </div>

        <!-- Team Members Table -->
        <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="card-title">
                        <Icon icon="mdi:account-group" class="w-5 h-5" />
                        สมาชิกทีม
                    </h3>
                    <button class="btn btn-sm btn-outline">
                        ดูทั้งหมด
                    </button>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="table table-zebra">
                        <thead>
                            <tr>
                                <th>สมาชิก</th>
                                <th>อีเมล</th>
                                <th>บทบาท</th>
                                <th>สถานะ</th>
                                <th>ออนไลน์ล่าสุด</th>
                                <th>สิทธิ์</th>
                                <th>การดำเนินการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {#each teamMembers as member}
                                <tr>
                                    <td>
                                        <div class="flex items-center gap-3">
                                            <div class="avatar placeholder">
                                                <div class="bg-neutral text-neutral-content rounded-full w-10">
                                                    <span class="text-sm">{member.avatar}</span>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="font-bold">{member.name}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{member.email}</td>
                                    <td>
                                        <span class="badge {getRoleBadge(member.role)}">
                                            {getRoleText(member.role)}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {getStatusBadge(member.status)}">
                                            {getStatusText(member.status)}
                                        </span>
                                    </td>
                                    <td>{member.lastActive}</td>
                                    <td>
                                        <div class="flex flex-wrap gap-1">
                                            {#each member.permissions as permission}
                                                <span class="badge badge-xs badge-outline">
                                                    {permission}
                                                </span>
                                            {/each}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="flex gap-1">
                                            <button class="btn btn-xs btn-outline">
                                                <Icon icon="mdi:eye" class="w-3 h-3" />
                                            </button>
                                            <button class="btn btn-xs btn-outline">
                                                <Icon icon="mdi:pencil" class="w-3 h-3" />
                                            </button>
                                            <button class="btn btn-xs btn-outline">
                                                <Icon icon="mdi:message" class="w-3 h-3" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{/if} 