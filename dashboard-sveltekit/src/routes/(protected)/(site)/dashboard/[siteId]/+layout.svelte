<script lang="ts">
    import { onMount } from "svelte";
    import { goto } from "$app/navigation";
    import { authStore } from "$lib/stores/auth.svelte";
    import Header from "$lib/components/layout/Header.svelte";
    import Sidebar from "$lib/components/layout/Sidebar.svelte";
    import Icon from "@iconify/svelte";
    let { children, data } = $props();
    
    // รอให้ authStore initialized และตรวจสอบ authentication
    const isReady = $derived(authStore.isInitialized);
    const isAuthenticated = $derived(authStore.isAuthenticated);
    
    // ตรวจสอบ error จาก server
    const hasError = $derived(data?.error);
    
    console.log('Layout:', {
        isReady,
        isAuthenticated,
        hasError,
        hasData: !!data
    });
    
    onMount(() => {
        // รอให้ authStore initialized ก่อน
        if (isReady && !isAuthenticated && !hasError) {
            console.log('Layout: User not authenticated, redirecting to signin');
            goto("/signin");
        }
    });
</script>

<div class="max-w-screen-2xl mx-auto">
    {#if !isReady}
        <div class="min-h-screen flex items-center justify-center">
            <div class="loading loading-spinner loading-lg"></div>
        </div>
    {:else if hasError}
        <div class="min-h-screen flex items-center justify-center">
            <div class="card bg-base-100 shadow-lg max-w-md">
                <div class="card-body text-center">
                    <div class="w-16 h-16 bg-warning/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Icon icon="mdi:alert-circle" class="w-8 h-8 text-warning" />
                    </div>
                    <h2 class="text-xl font-bold text-base-content mb-2">เกิดข้อผิดพลาด</h2>
                    <p class="text-base-content/60 mb-4">{hasError}</p>
                    <button class="btn btn-primary" onclick={() => window.location.reload()}>
                        ลองใหม่อีกครั้ง
                    </button>
                </div>
            </div>
        </div>
    {:else if !isAuthenticated}
        <div class="min-h-screen flex items-center justify-center">
            <div class="loading loading-spinner loading-lg"></div>
        </div>
    {:else}
        <div class="drawer lg:drawer-open">
            <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />

            <!-- Main Content -->
            <div class="drawer-content flex flex-col">
                <Header {data} />
                <main class="flex-1 p-3 sm:p-6">
                    {@render children()}
                </main>
            </div>

            <!-- Sidebar -->
            <Sidebar {data} />
        </div>
    {/if}
</div>
