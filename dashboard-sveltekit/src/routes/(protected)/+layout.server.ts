import type { LayoutServerLoad } from './$types';
import { requireAuth } from '$lib/utils/server-auth';

export const load: LayoutServerLoad = async ({ locals }) => {
  // ตรวจสอบ authentication
  try {
    requireAuth(locals);
    console.log('Protected Layout Server: Authentication successful');
    console.log('Protected Layout Server: locals.user:', locals.user);
    console.log('Protected Layout Server: locals.token:', locals.token ? 'exists' : 'not found');
    console.log('Protected Layout Server: locals.token:', locals.token);

    return {
      user: locals.user,
      token: locals.token
    };
  } catch (error) {
    console.log('Protected Layout Server: Authentication failed, but not redirecting automatically');
    console.log('Protected Layout Server: Error:', error);
    
    // ไม่ throw redirect ให้ client จัดการเอง
    return {
      user: null,
      token: null
    };
  }
}; 