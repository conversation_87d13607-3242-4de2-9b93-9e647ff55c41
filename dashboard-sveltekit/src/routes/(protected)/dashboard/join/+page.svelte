<script lang="ts">
	import { goto } from "$app/navigation";
	import { enhance } from "$app/forms";
	import { showSuccess, showError } from "$lib/utils/sweetalert";
	import Button from "$lib/components/ui/Button.svelte";
	import Card from "$lib/components/ui/Card.svelte";
	import Badge from "$lib/components/ui/Badge.svelte";
	import Icon from "@iconify/svelte";
	import SEO from "$lib/components/layout/SEO.svelte";
	import { onMount } from "svelte";

	let loading = $state(false);

	/**
	 * ✅ HYBRID APPROACH: Using server-side data and form actions
	 * - Data loaded server-side for better SEO and performance
	 * - Form actions for mutations with proper validation
	 * - Progressive enhancement with client-side feedback
	 */

	interface Invitation {
		_id: string;
		siteId: string;
		siteName: string;
		fromUserId: string;
		fromUserName: string;
		fromUserEmail: string;
		role: "owner" | "admin" | "editor" | "viewer";
		message?: string;
		status: "pending" | "accepted" | "rejected" | "expired";
		createdAt: string;
		expiresAt: string;
	}

	let { data } = $props();
	let invitations = $derived(data.invitations || []);
	let processingId: string | null = $state(null);

	// ฟังก์ชันสำหรับแสดงสีของ badge ตาม role
	function getRoleBadgeVariant(role: string) {
		switch (role) {
			case "owner":
				return "error";
			case "admin":
				return "primary";
			case "editor":
				return "secondary";
			case "viewer":
				return "neutral";
			default:
				return "neutral";
		}
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตาม status
	function getStatusBadgeVariant(status: string) {
		switch (status) {
			case "pending":
				return "warning";
			case "accepted":
				return "success";
			case "rejected":
				return "error";
			case "expired":
				return "neutral";
			default:
				return "neutral";
		}
	}

	// ฟังก์ชันสำหรับแสดงไอคอนตาม status
	function getStatusIcon(status: string) {
		switch (status) {
			case "pending":
				return "lucide:clock";
			case "accepted":
				return "lucide:check-circle";
			case "rejected":
				return "lucide:x-circle";
			case "expired":
				return "lucide:x-circle";
			default:
				return "lucide:clock";
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString("th-TH", {
			year: "numeric",
			month: "long",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	}

	// ตรวจสอบว่าคำเชิญหมดอายุหรือไม่
	function isExpired(expiresAt: string) {
		return new Date(expiresAt) < new Date();
	}

	onMount(() => {
		// fetchInvitations();
	});
</script>

<svelte:head>
	<title>เข้าร่วมทีมงาน - Dashboard</title>
</svelte:head>

<div class="container mx-auto space-y-6 sm:space-y-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">เข้าร่วมทีมงาน</h1>
			<p class="text-muted-foreground">
				จัดการคำเชิญเข้าร่วมทีมงานที่คุณได้รับ
			</p>
		</div>
		<Button variant="outline" onclick={() => goto("/dashboard")}>
			กลับไปหน้าหลัก
		</Button>
	</div>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div
				class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"
			></div>
		</div>
	{:else if invitations.length === 0}
		<Card>
			<Icon
				icon="lucide:users"
				class="h-12 w-12 text-muted-foreground mb-4"
			/>
			<h3 class="text-lg font-semibold mb-2">ไม่มีคำเชิญ</h3>
			<p class="text-muted-foreground text-center">
				คุณยังไม่มีคำเชิญเข้าร่วมทีมงานใดๆ<br />
				เมื่อมีคำเชิญใหม่ คุณจะเห็นรายการที่นี่
			</p>
		</Card>
	{:else}
		<div class="grid gap-4">
			{#each invitations as invitation (invitation._id)}
				{@const expired = isExpired(invitation.expiresAt)}
				{@const StatusIcon = getStatusIcon(invitation.status)}

				<Card>
					<Icon icon="lucide:building" class="h-5 w-5" />
					{invitation.siteName}

					<Icon icon="lucide:mail" class="h-4 w-4" />
					เชิญโดย {invitation.fromUserName} ({invitation.fromUserEmail})

					<Badge color={getRoleBadgeVariant(invitation.role)}>
						{invitation.role}
					</Badge>
					<Badge color={getStatusBadgeVariant(invitation.status)}>
						<Icon
							icon={getStatusIcon(invitation.status)}
							class="h-3 w-3 mr-1"
						/>
						{invitation.status === "pending"
							? "รอดำเนินการ"
							: invitation.status === "accepted"
								? "ยอมรับแล้ว"
								: invitation.status === "rejected"
									? "ปฏิเสธแล้ว"
									: "หมดอายุ"}
					</Badge>

					{#if invitation.message}
						<div class="bg-muted p-3 rounded-lg">
							<p class="text-sm">{invitation.message}</p>
						</div>
					{/if}

					<div
						class="flex items-center gap-4 text-sm text-muted-foreground"
					>
						<div class="flex items-center gap-1">
							<Icon icon="lucide:calendar" class="h-4 w-4" />
							ส่งเมื่อ: {formatDate(invitation.createdAt)}
						</div>
						<div class="flex items-center gap-1">
							<Icon icon="lucide:clock" class="h-4 w-4" />
							หมดอายุ: {formatDate(invitation.expiresAt)}
						</div>
					</div>

					{#if expired}
						<div
							class="bg-destructive/10 border border-destructive/20 rounded-lg p-3"
						>
							<p class="text-sm text-destructive font-medium">
								คำเชิญนี้หมดอายุแล้ว
							</p>
						</div>
					{/if}

					{#if invitation.status === "pending" && !expired}
						<!-- <Separator /> -->
						<div class="flex gap-2">
							<Button
								onclick={() => {}}
								disabled={processingId === invitation._id}
								class="flex-1"
							>
								{#if processingId === invitation._id}
									<div
										class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
									></div>
								{:else}
									<Icon
										icon="lucide:check-circle"
										class="h-4 w-4 mr-2"
									/>
								{/if}
								ยอมรับ
							</Button>
							<Button
								variant="outline"
								onclick={() => {}}
								disabled={processingId === invitation._id}
								class="flex-1"
							>
								{#if processingId === invitation._id}
									<div
										class="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"
									></div>
								{:else}
									<Icon
										icon="lucide:x-circle"
										class="h-4 w-4 mr-2"
									/>
								{/if}
								ปฏิเสธ
							</Button>
						</div>
					{/if}
				</Card>
			{/each}
		</div>
	{/if}
</div>
