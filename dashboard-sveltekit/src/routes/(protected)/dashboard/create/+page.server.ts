import type { PageServerLoad, Actions } from './$types';
import { fail, redirect } from '@sveltejs/kit';
import { siteService } from '$lib/services/site';
import { subscriptionService } from '$lib/services/subscription';
import type { CreateSiteData, CheckDomainData } from '$lib/schemas/site.schema';

/**
 * ✅ HYBRID APPROACH: SvelteKit Route API + Service Pattern
 * - Route-level validation and error handling
 * - Service layer for business logic
 * - Consistent fail() responses
 * - Type-safe data flow
 */

export const load: PageServerLoad = async ({ locals }) => {
  // Auth check already done in layout
  try {
    // ✅ Load packages for site creation
    const packagesResult = await subscriptionService.getPackages(locals.token!);

    return {
      packages: packagesResult.success && packagesResult.data ?
        (Array.isArray(packagesResult.data) ? packagesResult.data : (packagesResult.data as any)?.packages || []) : []
    };
  } catch (error) {
    console.error('Error loading packages:', error);
    return {
      packages: []
    };
  }
};

export const actions: Actions = {
  /**
   * ✅ Check domain availability
   */
  checkDomain: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // Extract form data
      const domainData: CheckDomainData = {
        typeDomain: data.get('typeDomain') as 'subdomain' | 'custom',
        subDomain: data.get('subDomain')?.toString(),
        mainDomain: data.get('mainDomain')?.toString(),
        customDomain: data.get('customDomain')?.toString()
      };

      // Call site service (validation included)
      const result = await siteService.checkDomain(domainData, locals.token);

      if (!result.success) {
        return fail(400, {
          error: result.error,
          type: 'domain'
        });
      }

      return {
        success: true,
        data: result.data,
        message: result.data?.available ? 'โดเมนพร้อมใช้งาน' : 'โดเมนไม่พร้อมใช้งาน',
        type: 'domain'
      };

    } catch (error) {
      console.error('Check domain error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการตรวจสอบโดเมน',
        type: 'domain'
      });
    }
  },

  /**
   * ✅ Create new site
   */
  createSite: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // Extract form data
      const siteData: CreateSiteData = {
        siteName: data.get('siteName')?.toString() || '',
        typeDomain: data.get('typeDomain') as 'subdomain' | 'custom',
        subDomain: data.get('subDomain')?.toString(),
        mainDomain: data.get('mainDomain')?.toString(),
        customDomain: data.get('customDomain')?.toString(),
        packageType: data.get('packageType')?.toString() || ''
      };

      // Call site service (validation included)
      const result = await siteService.createSite(siteData, locals.token!);

      if (!result.success) {
        return fail(400, {
          error: result.error,
          type: 'create'
        });
      }

      // Redirect to the new site dashboard
      throw redirect(303, `/dashboard/${result.data?.id || result.data?._id}`);

    } catch (error) {
      console.error('Create site error:', error);

      if (error instanceof Response) {
        throw error; // Re-throw redirects
      }

      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์',
        type: 'create'
      });
    }
  }
};