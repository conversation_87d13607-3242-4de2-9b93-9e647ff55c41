<script lang="ts">
  import { page } from "$app/stores";
  import { authStore } from "$lib/stores/auth.svelte";
  import { showSuccess, showError, showInfo } from "$lib/utils/sweetalert";
  import Icon from "@iconify/svelte";
  import SEO from "$lib/components/layout/SEO.svelte";
  import Card from "$lib/components/ui/Card.svelte";
  import Button from "$lib/components/ui/Button.svelte";
  import Input from "$lib/components/ui/Input.svelte";
  import Badge from "$lib/components/ui/Badge.svelte";
  import Image from "$lib/components/ui/Image.svelte";
  import StaggeredList from "$lib/components/transitions/StaggeredList.svelte";
  import LoadingTransition from "$lib/components/transitions/LoadingTransition.svelte";
  import { enhance } from "$app/forms";
  import { fade } from "svelte/transition";

  const { data } = $props();

  const user = $derived(data?.user || authStore.user);
  let isLoading = $state(false);
  let isRefreshing = $state(false);

  // ข้อมูลโปรไฟล์
  const profileData = $state({
    firstName: "",
    lastName: "",
    email: "",
    avatar: "",
    cover: "",
    moneyPoint: 0,
    goldPoint: 0,
    isEmailVerified: false,
    role: "user",
    status: "active",
    createdAt: "",
    updatedAt: "",
  });

  // ข้อมูลประวัติและกิจกรรม
  const activityData = $state([
    {
      id: 1,
      type: "profile_update",
      title: "อัปเดตข้อมูลโปรไฟล์",
      description: "เปลี่ยนชื่อและนามสกุล",
      timestamp: new Date().toISOString(),
      icon: "mdi:account-edit",
      color: "text-blue-500",
    },
    {
      id: 2,
      type: "login",
      title: "เข้าสู่ระบบ",
      description: "เข้าสู่ระบบจาก IP: ***********",
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      icon: "mdi:login",
      color: "text-green-500",
    },
    {
      id: 3,
      type: "points_earned",
      title: "ได้รับ Money Points",
      description: "ได้รับ 100 Money Points จากการทำภารกิจ",
      timestamp: new Date(Date.now() - 7200000).toISOString(),
      icon: "mdi:coin",
      color: "text-yellow-500",
    },
    {
      id: 4,
      type: "email_verified",
      title: "ยืนยันอีเมล",
      description: "ยืนยันอีเมลเรียบร้อยแล้ว",
      timestamp: new Date(Date.now() - ********).toISOString(),
      icon: "mdi:email-check",
      color: "text-green-500",
    },
  ]);

  // สถิติผู้ใช้
  const userStats = $state({
    totalLogins: 42,
    totalPoints: 1250,
    joinedDays: 30,
    lastActive: new Date().toISOString(),
  });

  // State สำหรับ UI
  let activeTab = $state("profile");
  let showImageUpload = $state(false);

  // อัปเดตข้อมูลเมื่อ user เปลี่ยน
  $effect(() => {
    if (user) {
      profileData.firstName = user.firstName || "";
      profileData.lastName = user.lastName || "";
      profileData.email = user.email || "";
      profileData.avatar = user.avatar || "";
      profileData.cover = user.cover || "";
      profileData.moneyPoint = user.moneyPoint || 0;
      profileData.goldPoint = user.goldPoint || 0;
      profileData.isEmailVerified = user.isEmailVerified || false;
      profileData.role = user.role || "user";
      profileData.status = user.status || "active";
      profileData.createdAt = user.createdAt || "";
      profileData.updatedAt = user.updatedAt || "";
    }
  });

  // ฟังก์ชันจัดการผลลัพธ์จาก server actions
  function handleActionResult(result: any) {
    console.log("handleActionResult", result);
    if (result.type === "success") {
      // อัปเดตข้อมูลใน auth store ถ้ามี user data
      if (result.data?.user) {
        authStore.updateUser(result.data.user);
      }
      showSuccess(result.data?.message || "ดำเนินการสำเร็จ!");
    } else if (result.type === "failure") {
      showError(result.data?.error || "เกิดข้อผิดพลาด");
    }
  }

  // ฟังก์ชันจัดการ form submission
  function handleFormSubmit() {
    isLoading = true;
  }

  function handleFormResult(result: any) {
    isLoading = false;
    handleActionResult(result);
  }

  function handleFieldChange(
    field: keyof typeof profileData,
    value: string | number | boolean,
  ) {
    (profileData as any)[field] = value;
  }
</script>

<SEO
  title="โปรไฟล์ - จัดการข้อมูลส่วนตัว"
  description="จัดการข้อมูลส่วนตัว อัปเดตโปรไฟล์ และทดสอบ refresh token"
  keywords="โปรไฟล์, ข้อมูลส่วนตัว, refresh token, authentication"
  url="/dashboard/profile"
  noindex={true}
/>

<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Profile Header with Cover & Avatar -->
  <div class="relative mb-8 animate-fade-in">
    <!-- Cover Image -->
    <div
      class="relative h-48 md:h-64 bg-gradient-to-r from-primary to-secondary rounded-t-2xl overflow-hidden"
    >
      {#if profileData.cover}
        <Image
          publicId={profileData.cover}
          width={1600}
          height={600}
          cover={true}
          center={true}
          alt="Cover Image"
          class="w-full h-full object-cover"
          lazy
        />
      {:else}
        <div
          class="w-full h-full bg-gradient-to-br from-primary/80 via-secondary/60 to-accent/40 flex items-center justify-center"
        >
          <Icon icon="mdi:image" class="w-16 h-16 text-white/50" />
        </div>
      {/if}

      <!-- Cover Upload Button -->
      <button
        class="absolute top-4 right-4 btn btn-sm btn-circle bg-black/20 border-none text-white hover:bg-black/40"
        onclick={() => (showImageUpload = !showImageUpload)}
      >
        <Icon icon="mdi:camera" class="w-4 h-4" />
      </button>
    </div>

    <!-- Profile Info Section -->
    <div class="relative bg-base-100 rounded-b-2xl shadow-xl p-6">
      <!-- Avatar -->
      <div class="absolute -top-16 left-6">
        <div class="relative">
          <div
            class="w-32 h-32 rounded-full border-4 border-base-100 shadow-xl overflow-hidden bg-base-200"
          >
            {#if profileData.avatar}
              <Image
                publicId={profileData.avatar}
                alt="Avatar"
                width={128}
                height={128}
                cover={true}
                center={true}
                class="w-full h-full object-cover"
                lazy
              />
            {:else}
              <div
                class="w-full h-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center"
              >
                <Icon icon="mdi:account" class="w-16 h-16 text-white" />
              </div>
            {/if}
          </div>

          <!-- Avatar Upload Button -->
          <button
            class="absolute bottom-2 right-2 btn btn-xs btn-circle btn-primary"
            onclick={() => (showImageUpload = !showImageUpload)}
          >
            <Icon icon="mdi:camera" class="w-3 h-3" />
          </button>
        </div>
      </div>

      <!-- Profile Header Info -->
      <div class="ml-40">
        <div
          class="flex flex-col md:flex-row md:items-center md:justify-between gap-4"
        >
          <div>
            {#if profileData.firstName || profileData.lastName}
              <h1 class="text-3xl font-bold text-base-content mb-2">
                {profileData.firstName}
                {profileData.lastName}
              </h1>
            {/if}
            <div class="flex items-center gap-4 text-base-content/70">
              <div class="flex items-center gap-2">
                <Icon icon="mdi:email" class="w-4 h-4" />
                <span>{profileData.email}</span>
                {#if profileData.isEmailVerified}
                  <Badge label="ยืนยันแล้ว" color="success" size="sm" />
                {/if}
              </div>
            </div>
            <div
              class="flex items-center gap-4 mt-2 text-sm text-base-content/60"
            >
              <div class="flex items-center gap-1">
                <Icon icon="mdi:calendar" class="w-4 h-4" />
                <span
                  >เข้าร่วมเมื่อ {profileData.createdAt
                    ? new Date(profileData.createdAt).toLocaleDateString(
                        "th-TH",
                      )
                    : "ไม่ระบุ"}</span
                >
              </div>
              <div class="flex items-center gap-1">
                <Icon icon="mdi:clock" class="w-4 h-4" />
                <span
                  >ออนไลน์ล่าสุด {new Date(userStats.lastActive).toLocaleString(
                    "th-TH",
                  )}</span
                >
              </div>
            </div>
          </div>

          <!-- Quick Stats -->
          <div class="flex gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-primary">
                {profileData.moneyPoint.toLocaleString()}
              </div>
              <div class="text-xs text-base-content/60">Money Points</div>
            </div>
            <!-- <div class="text-2xl font-bold text-secondary">/</div> -->
            <div class="text-center">
              <div class="text-2xl font-bold text-warning">
                {profileData.goldPoint.toLocaleString()}
              </div>
              <div class="text-xs text-base-content/60">Gold Points</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Tab Navigation -->
  <div class="tabs tabs-boxed mb-6 animate-fade-in bg-base-100 rounded-lg p-1 w-fit mx-auto">
    <button
      class="tab {activeTab === 'profile' ? 'tab-active' : ''}"
      onclick={() => (activeTab = "profile")}
    >
      <Icon icon="mdi:account" class="w-4 h-4 mr-2" />
      ข้อมูลโปรไฟล์
    </button>
    <button
      class="tab {activeTab === 'activity' ? 'tab-active' : ''}"
      onclick={() => (activeTab = "activity")}
    >
      <Icon icon="mdi:history" class="w-4 h-4 mr-2" />
      ประวัติกิจกรรม
    </button>
    <button
      class="tab {activeTab === 'stats' ? 'tab-active' : ''}"
      onclick={() => (activeTab = "stats")}
    >
      <Icon icon="mdi:chart-line" class="w-4 h-4 mr-2" />
      สถิติ
    </button>
    <button
      class="tab {activeTab === 'settings' ? 'tab-active' : ''}"
      onclick={() => (activeTab = "settings")}
    >
      <Icon icon="mdi:cog" class="w-4 h-4 mr-2" />
      การตั้งค่า
    </button>
  </div>

  <!-- Image Upload Modal -->
  {#if showImageUpload}
    <div
      class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 animate-fade-in"
      transition:fade
    >
      <div class="bg-base-100 rounded-2xl p-6 max-w-md w-full mx-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">อัปโหลดรูปภาพ</h3>
          <button
            class="btn btn-sm btn-circle btn-ghost"
            onclick={() => (showImageUpload = false)}
          >
            <Icon icon="mdi:close" class="w-4 h-4" />
          </button>
        </div>

        <div class="space-y-4">
          <div class="text-center">
            <div
              class="border-2 border-dashed border-base-300 rounded-lg p-8 hover:border-primary transition-colors"
            >
              <Icon
                icon="mdi:cloud-upload"
                class="w-12 h-12 mx-auto text-base-content/50 mb-2"
              />
              <p class="text-sm text-base-content/70">
                ลากไฟล์มาวางที่นี่ หรือคลิกเพื่อเลือกไฟล์
              </p>
              <input type="file" accept="image/*" class="hidden" />
            </div>
          </div>

          <div class="flex gap-2">
            <Button
              color="primary"
              block
              onclick={() => (showImageUpload = false)}
            >
              <Icon icon="mdi:upload" class="w-4 h-4 mr-2" />
              อัปโหลด
            </Button>
            <Button variant="ghost" onclick={() => (showImageUpload = false)}>
              ยกเลิก
            </Button>
          </div>
        </div>
      </div>
    </div>
  {/if}

  <!-- Tab Content -->
  <div class="animate-fade-in">
    {#if activeTab === "profile"}
      <!-- Profile Tab Content -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Profile Form -->
        <Card title="แก้ไขข้อมูลโปรไฟล์" size="full">
          <form
            method="POST"
            action="?/updateProfile"
            use:enhance={() => {
              handleFormSubmit();
              return async ({ result }) => {
                handleFormResult(result);
              };
            }}
          >
            <div class="space-y-4">
              <Input
                type="text"
                name="firstName"
                value={profileData.firstName}
                label="ชื่อ"
                placeholder="ชื่อ"
                icon="mdi:account"
                onchange={(e) =>
                  handleFieldChange(
                    "firstName",
                    (e.target as HTMLInputElement).value,
                  )}
              />

              <Input
                type="text"
                name="lastName"
                value={profileData.lastName}
                label="นามสกุล"
                placeholder="นามสกุล"
                icon="mdi:account"
                onchange={(e) =>
                  handleFieldChange(
                    "lastName",
                    (e.target as HTMLInputElement).value,
                  )}
              />

              <Input
                type="email"
                value={profileData.email}
                label="อีเมล"
                placeholder="<EMAIL>"
                icon="mdi:email"
                disabled
              />

              <div class="flex items-center gap-2">
                <span class="text-sm text-base-content/70">สถานะอีเมล:</span>
                <Badge
                  label={profileData.isEmailVerified
                    ? "ยืนยันแล้ว"
                    : "ยังไม่ยืนยัน"}
                  color={profileData.isEmailVerified ? "success" : "warning"}
                  size="sm"
                />
              </div>

              <Button
                type="submit"
                color="primary"
                loading={isLoading}
                disabled={isLoading}
                block
              >
                <Icon icon="mdi:content-save" class="w-4 h-4 mr-2" />
                บันทึกการเปลี่ยนแปลง
              </Button>
            </div>
          </form>
        </Card>

        <!-- Account Info -->
        <Card title="ข้อมูลบัญชี" size="full">
          <div class="space-y-6">
            <!-- Points Display -->
            <!-- <div class="grid grid-cols-2 gap-4">
              <div
                class="bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 p-4 rounded-xl border border-yellow-500/20"
              >
                <div class="flex items-center gap-3 mb-2">
                  <div
                    class="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center"
                  >
                    <Icon icon="mdi:coin" class="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div class="text-sm text-base-content/70">Money Points</div>
                    <div class="text-2xl font-bold text-base-content">
                      {profileData.moneyPoint.toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="bg-gradient-to-br from-amber-500/10 to-amber-600/10 p-4 rounded-xl border border-amber-500/20"
              >
                <div class="flex items-center gap-3 mb-2">
                  <div
                    class="w-10 h-10 bg-amber-500 rounded-full flex items-center justify-center"
                  >
                    <Icon icon="mdi:star" class="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div class="text-sm text-base-content/70">Gold Points</div>
                    <div class="text-2xl font-bold text-base-content">
                      {profileData.goldPoint.toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>
            </div> -->

            <!-- Account Details -->
            <div class="space-y-3">
              <div
                class="flex items-center justify-between p-3 bg-base-200 rounded-lg"
              >
                <span class="text-sm text-base-content/70">บทบาท:</span>
                <Badge label={profileData.role} color="primary" size="sm" />
              </div>

              <div
                class="flex items-center justify-between p-3 bg-base-200 rounded-lg"
              >
                <span class="text-sm text-base-content/70">สถานะบัญชี:</span>
                <Badge
                  label={profileData.status === "active"
                    ? "ใช้งาน"
                    : "ระงับการใช้งาน"}
                  color={profileData.status === "active" ? "success" : "error"}
                  size="sm"
                />
              </div>

              <div
                class="flex items-center justify-between p-3 bg-base-200 rounded-lg"
              >
                <span class="text-sm text-base-content/70">User ID:</span>
                <span class="text-xs font-mono text-base-content/80">
                  {user?._id || "ไม่ระบุ"}
                </span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    {:else if activeTab === "activity"}
      <!-- Activity Tab Content -->
      <Card title="ประวัติกิจกรรม" size="full">
        <StaggeredList items={activityData}>
          {#snippet children(activity, index)}
            <div
              class="flex items-start gap-4 p-4 bg-base-200/50 rounded-lg hover:bg-base-200 transition-colors"
            >
              <div
                class="w-10 h-10 rounded-full bg-base-100 flex items-center justify-center flex-shrink-0"
              >
                <Icon icon={activity.icon} class="w-5 h-5 {activity.color}" />
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between mb-1">
                  <h4 class="font-medium text-base-content">
                    {activity.title}
                  </h4>
                  <span class="text-xs text-base-content/60">
                    {new Date(activity.timestamp).toLocaleString("th-TH")}
                  </span>
                </div>
                <p class="text-sm text-base-content/70">
                  {activity.description}
                </p>
              </div>
            </div>
          {/snippet}
        </StaggeredList>
      </Card>
    {:else if activeTab === "stats"}
      <!-- Stats Tab Content -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Login Stats -->
        <Card title="สถิติการเข้าสู่ระบบ"  size="full">
          <div class="space-y-4">
            <div class="text-center">
              <div class="text-4xl font-bold text-primary mb-2">
                {userStats.totalLogins}
              </div>
              <div class="text-sm text-base-content/70">ครั้งทั้งหมด</div>
            </div>
            <div class="bg-base-200 p-3 rounded-lg">
              <div class="text-xs text-base-content/60 mb-1">
                เข้าสู่ระบบล่าสุด
              </div>
              <div class="text-sm font-medium">
                {new Date(userStats.lastActive).toLocaleString("th-TH")}
              </div>
            </div>
          </div>
        </Card>

        <!-- Points Stats -->
        <Card title="สถิติคะแนน"  size="full">
          <div class="space-y-4">
            <div class="text-center">
              <div class="text-4xl font-bold text-secondary mb-2">
                {userStats.totalPoints}
              </div>
              <div class="text-sm text-base-content/70">คะแนนรวม</div>
            </div>
            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-base-content/70">Money Points:</span>
                <span class="font-medium"
                  >{profileData.moneyPoint.toLocaleString()}</span
                >
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-base-content/70">Gold Points:</span>
                <span class="font-medium"
                  >{profileData.goldPoint.toLocaleString()}</span
                >
              </div>
            </div>
          </div>
        </Card>

        <!-- Membership Stats -->
        <Card title="สถิติสมาชิก"  size="full">
          <div class="space-y-4">
            <div class="text-center">
              <div class="text-4xl font-bold text-accent mb-2">
                {userStats.joinedDays}
              </div>
              <div class="text-sm text-base-content/70">วันที่เป็นสมาชิก</div>
            </div>
            <div class="bg-base-200 p-3 rounded-lg">
              <div class="text-xs text-base-content/60 mb-1">เข้าร่วมเมื่อ</div>
              <div class="text-sm font-medium">
                {profileData.createdAt
                  ? new Date(profileData.createdAt).toLocaleDateString("th-TH")
                  : "ไม่ระบุ"}
              </div>
            </div>
          </div>
        </Card>
      </div>
    {:else if activeTab === "settings"}
      <!-- Settings Tab Content -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- System Test -->
        <Card title="ทดสอบระบบ"  size="full">
          <div class="space-y-4">
            <!-- รีโหลดข้อมูลโปรไฟล์ -->
            <form
              method="POST"
              action="?/refreshProfile"
              use:enhance={() => {
                isRefreshing = true;
                return async ({ result }) => {
                  isRefreshing = false;
                  handleActionResult(result);
                };
              }}
            >
              <Button
                type="submit"
                color="primary"
                loading={isRefreshing}
                disabled={isRefreshing}
                block
              >
                <Icon icon="mdi:refresh" class="w-4 h-4 mr-2" />
                รีโหลดข้อมูลโปรไฟล์
              </Button>
            </form>

            <!-- ทดสอบ Refresh Token -->
            <form
              method="POST"
              action="?/testToken"
              use:enhance={() => {
                isLoading = true;
                return async ({ result }) => {
                  isLoading = false;
                  handleActionResult(result);
                };
              }}
            >
              <Button
                type="submit"
                color="secondary"
                loading={isLoading}
                disabled={isLoading}
                block
              >
                <Icon icon="mdi:shield-check" class="w-4 h-4 mr-2" />
                ทดสอบ Refresh Token
              </Button>
            </form>

            <Button
              variant="ghost"
              block
              onclick={() =>
                showInfo(
                  "ข้อมูลการทดสอบ",
                  "ใช้ปุ่มเหล่านี้เพื่อทดสอบการทำงานของ refresh token และการอัปเดตข้อมูลโปรไฟล์",
                )}
            >
              <Icon icon="mdi:help-circle" class="w-4 h-4 mr-2" />
              วิธีใช้งาน
            </Button>
          </div>
        </Card>

        <!-- Token Information -->
        <Card title="ข้อมูล Token"  size="full">
          <div class="space-y-4">
            <div class="bg-base-200 p-4 rounded-lg">
              <h4 class="font-semibold text-base-content mb-3">
                ข้อมูลการเข้าสู่ระบบ
              </h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-base-content/70">สร้างเมื่อ:</span>
                  <span class="text-base-content">
                    {profileData.createdAt
                      ? new Date(profileData.createdAt).toLocaleString("th-TH")
                      : "ไม่ระบุ"}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-base-content/70">อัปเดตล่าสุด:</span>
                  <span class="text-base-content">
                    {profileData.updatedAt
                      ? new Date(profileData.updatedAt).toLocaleString("th-TH")
                      : "ไม่ระบุ"}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-base-content/70">สถานะ:</span>
                  <Badge label="เข้าสู่ระบบแล้ว" color="success" size="sm" />
                </div>
              </div>
            </div>

            <div class="bg-base-200 p-4 rounded-lg">
              <h4 class="font-semibold text-base-content mb-3">
                คำแนะนำการทดสอบ
              </h4>
              <div class="text-sm text-base-content/70 space-y-2">
                <p>
                  1. <strong>รีโหลดข้อมูลโปรไฟล์:</strong> ทดสอบการดึงข้อมูลใหม่จาก
                  API
                </p>
                <p>
                  2. <strong>ทดสอบ Refresh Token:</strong> ทดสอบการ refresh token
                  เมื่อหมดอายุ
                </p>
                <p>
                  3. <strong>บันทึกการเปลี่ยนแปลง:</strong> ทดสอบการอัปเดตข้อมูลโปรไฟล์
                </p>
                <p class="text-warning">
                  💡 เปิด Developer Console เพื่อดู log การทำงานของ token
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    {/if}
  </div>
</div>
