<script lang="ts">
    import LoadingDemo from '$lib/components/loading/LoadingDemo.svelte';
    import { loading } from '$lib/stores/loading.svelte';
    import { onMount } from 'svelte';

    let pageTitle = 'Loading System Demo';
    
    // Demo functions for quick testing
    async function quickDemo() {
        const id = loading.show('กำลังทดสอบระบบ...', {
            type: 'spinner',
            size: 'lg',
            color: 'primary',
            overlay: true,
            blur: true
        });
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        loading.hide(id);
    }

    async function progressDemo() {
        await loading.withProgress(async (updateProgress) => {
            for (let i = 0; i <= 100; i += 5) {
                updateProgress(i, `กำลังประมวลผล... ${i}%`);
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }, 'กำลังอัปโหลดไฟล์...', {
            type: 'progress',
            size: 'xl',
            color: 'success'
        });
    }

    onMount(() => {
        // Show initialization loading
        const id = loading.initializing('กำลังโหลดหน้าเดโม...');
        setTimeout(() => loading.hide(id), 1500);
    });
</script>

<svelte:head>
    <title>{pageTitle} - Dashboard</title>
    <meta name="description" content="ทดสอบระบบ Loading Screen ที่ยืดหยุ่นและปรับแต่งได้" />
</svelte:head>

<div class="loading-demo-page">
    <!-- Page Header -->
    <div class="page-header mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-base-content mb-2">
                    🔄 Loading System Demo
                </h1>
                <p class="text-base-content/70">
                    ทดสอบระบบ Loading Screen ที่ยืดหยุ่นและปรับแต่งได้ผ่าน Environment Variables
                </p>
            </div>
            <div class="flex gap-2">
                <button class="btn btn-primary" onclick={quickDemo}>
                    ⚡ Quick Demo
                </button>
                <button class="btn btn-success" onclick={progressDemo}>
                    📊 Progress Demo
                </button>
            </div>
        </div>
    </div>

    <!-- Environment Variables Status -->
    <div class="env-status mb-8">
        <div class="alert alert-info">
            <div class="flex items-center gap-2">
                <span class="text-lg">ℹ️</span>
                <div>
                    <h3 class="font-semibold">Environment Configuration</h3>
                    <p class="text-sm">
                        ระบบ Loading ปรับแต่งได้ผ่าน Environment Variables ใน <code>.env</code> file
                        <br>
                        ดูตัวอย่างใน <code>.env.example</code> และ documentation ใน README.md
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Component -->
    <LoadingDemo />

    <!-- Usage Examples -->
    <div class="usage-examples mt-12">
        <h2 class="text-2xl font-bold mb-6">📝 Usage Examples</h2>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Usage -->
            <div class="example-card bg-base-200 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">🔧 Basic Usage</h3>
                <div class="mockup-code">
                    <pre data-prefix="1"><code>&lt;script&gt;</code></pre>
                    <pre data-prefix="2"><code>  import LoadingScreen from '$lib/components/loading/LoadingScreen.svelte';</code></pre>
                    <pre data-prefix="3"><code>  let isLoading = $state(false);</code></pre>
                    <pre data-prefix="4"><code>&lt;/script&gt;</code></pre>
                    <pre data-prefix="5"><code></code></pre>
                    <pre data-prefix="6"><code>&lt;LoadingScreen &#123;isLoading&#125; message="กำลังโหลด..."&gt;</code></pre>
                    <pre data-prefix="7"><code>  &lt;YourContent /&gt;</code></pre>
                    <pre data-prefix="8"><code>&lt;/LoadingScreen&gt;</code></pre>
                </div>
            </div>

            <!-- Global Store Usage -->
            <div class="example-card bg-base-200 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">🌐 Global Store Usage</h3>
                <div class="mockup-code">
                    <pre data-prefix="1"><code>&lt;script&gt;</code></pre>
                    <pre data-prefix="2"><code>  import &#123; loading &#125; from '$lib/stores/loading.svelte';</code></pre>
                    <pre data-prefix="3"><code></code></pre>
                    <pre data-prefix="4"><code>  async function saveData() &#123;</code></pre>
                    <pre data-prefix="5"><code>    const id = loading.saving('กำลังบันทึก...');</code></pre>
                    <pre data-prefix="6"><code>    await api.save(data);</code></pre>
                    <pre data-prefix="7"><code>    loading.hide(id);</code></pre>
                    <pre data-prefix="8"><code>  &#125;</code></pre>
                    <pre data-prefix="9"><code>&lt;/script&gt;</code></pre>
                </div>
            </div>

            <!-- Environment Variables -->
            <div class="example-card bg-base-200 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">⚙️ Environment Variables</h3>
                <div class="mockup-code">
                    <pre data-prefix="1"><code># .env</code></pre>
                    <pre data-prefix="2"><code>PUBLIC_LOADING_TYPE="spinner"</code></pre>
                    <pre data-prefix="3"><code>PUBLIC_LOADING_SIZE="lg"</code></pre>
                    <pre data-prefix="4"><code>PUBLIC_LOADING_COLOR="primary"</code></pre>
                    <pre data-prefix="5"><code>PUBLIC_LOADING_ANIMATION="fade"</code></pre>
                    <pre data-prefix="6"><code>PUBLIC_LOADING_DURATION="300"</code></pre>
                    <pre data-prefix="7"><code>PUBLIC_LOADING_DISABLED="false"</code></pre>
                </div>
            </div>

            <!-- Progress Loading -->
            <div class="example-card bg-base-200 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">📊 Progress Loading</h3>
                <div class="mockup-code">
                    <pre data-prefix="1"><code>await loading.withProgress(async (updateProgress) =&gt; &#123;</code></pre>
                    <pre data-prefix="2"><code>  for (let i = 0; i &lt;= 100; i += 10) &#123;</code></pre>
                    <pre data-prefix="3"><code>    await processChunk(i);</code></pre>
                    <pre data-prefix="4"><code>    updateProgress(i, `Processing $&#123;i&#125;%`);</code></pre>
                    <pre data-prefix="5"><code>  &#125;</code></pre>
                    <pre data-prefix="6"><code>&#125;, 'กำลังประมวลผล...', &#123; type: 'progress' &#125;);</code></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Features List -->
    <div class="features-list mt-12">
        <h2 class="text-2xl font-bold mb-6">✨ Features</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="feature-card bg-primary/10 p-4 rounded-lg border border-primary/20">
                <div class="text-2xl mb-2">🎨</div>
                <h3 class="font-semibold mb-2">6 Loading Types</h3>
                <p class="text-sm text-base-content/70">
                    Spinner, Dots, Bars, Pulse, Skeleton, Progress
                </p>
            </div>

            <div class="feature-card bg-secondary/10 p-4 rounded-lg border border-secondary/20">
                <div class="text-2xl mb-2">📏</div>
                <h3 class="font-semibold mb-2">4 Size Options</h3>
                <p class="text-sm text-base-content/70">
                    Small, Medium, Large, Extra Large
                </p>
            </div>

            <div class="feature-card bg-accent/10 p-4 rounded-lg border border-accent/20">
                <div class="text-2xl mb-2">🌈</div>
                <h3 class="font-semibold mb-2">7 Color Themes</h3>
                <p class="text-sm text-base-content/70">
                    Primary, Secondary, Accent, Info, Success, Warning, Error
                </p>
            </div>

            <div class="feature-card bg-info/10 p-4 rounded-lg border border-info/20">
                <div class="text-2xl mb-2">🎭</div>
                <h3 class="font-semibold mb-2">4 Animations</h3>
                <p class="text-sm text-base-content/70">
                    Fade, Slide Up, Scale, None
                </p>
            </div>

            <div class="feature-card bg-success/10 p-4 rounded-lg border border-success/20">
                <div class="text-2xl mb-2">📊</div>
                <h3 class="font-semibold mb-2">Progress Tracking</h3>
                <p class="text-sm text-base-content/70">
                    Real-time progress with percentage display
                </p>
            </div>

            <div class="feature-card bg-warning/10 p-4 rounded-lg border border-warning/20">
                <div class="text-2xl mb-2">⚙️</div>
                <h3 class="font-semibold mb-2">Environment Config</h3>
                <p class="text-sm text-base-content/70">
                    Configurable via .env variables
                </p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="demo-footer mt-12 pt-8 border-t border-base-300">
        <div class="text-center text-base-content/60">
            <p class="mb-2">
                🔄 Enhanced Loading System สำหรับ SvelteKit 5 + DaisyUI 5
            </p>
            <p class="text-sm">
                ปรับแต่งได้ผ่าน Environment Variables • รองรับ TypeScript • Responsive Design
            </p>
        </div>
    </div>
</div>

<style>
    .loading-demo-page {
        container-type: inline-size;
        padding: 1rem;
    }

    .example-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .example-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .feature-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .feature-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    code {
        background: hsl(var(--b3));
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-size: 0.875em;
        font-family: 'Courier New', monospace;
    }

    @container (max-width: 768px) {
        .loading-demo-page {
            padding: 0.5rem;
        }
        
        .page-header .flex {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
        }
    }
</style>
