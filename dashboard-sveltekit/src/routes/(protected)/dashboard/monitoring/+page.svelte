<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { authStore } from '$lib/stores/auth.svelte';
  import { apiClient } from '$lib/api/client';
  import { showSuccess, showError, showLoading } from '$lib/utils/sweetalert';
  import { logger, LogCategory } from '$lib/utils/logger';
  
  import Card from '$lib/components/ui/Card.svelte';
  import Button from '$lib/components/ui/Button.svelte';
  import Badge from '$lib/components/ui/Badge.svelte';
  import Table from '$lib/components/ui/Table.svelte';
  import Alert from '$lib/components/ui/Alert.svelte';
  import LoadingScreen from '$lib/components/loading/LoadingScreen.svelte';

  interface MonitoringStats {
    monitoring: {
      totalTokenActivities: number;
      totalSecurityEvents: number;
      recentTokenActivities: any[];
      recentSecurityEvents: any[];
    };
    rateLimit: {
      totalRecords: number;
      blockedRecords: number;
      activeRecords: number;
    };
    tokenStore: {
      totalTokens: number;
      activeTokens: number;
      revokedTokens: number;
      recentTokens: any[];
    };
  }

  let stats: MonitoringStats | null = $state(null);
  let loading = $state(true);
  let error: string | null = $state(null);
  let refreshInterval: number = $state(0);

  onMount(() => {
    // Check if user is admin
    if (authStore.user?.role !== 'admin') {
      showError('ไม่มีสิทธิ์เข้าถึง');
      goto('/dashboard');
      return;
    }

      loadStats();
    // Auto refresh every 30 seconds
    const interval = setInterval(loadStats, 30000);

    return () => {
      clearInterval(interval);
      }
    });

  async function loadStats() {
    try {
      loading = true;
      error = null;

      const response:any = await apiClient.request('/monitoring/stats', { method: 'GET' });
      
      if (response.success) {
        stats = response.data;
        logger.info(LogCategory.SYSTEM, 'monitoring_stats_loaded', 'Monitoring stats loaded successfully');
      } else {
        throw new Error(response.message || 'Failed to load monitoring stats');
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
      logger.error(LogCategory.SYSTEM, 'monitoring_stats_error', 'Failed to load monitoring stats', { error: err });
    } finally {
      loading = false;
    }
  }

  function getSeverityColor(severity: string) {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'neutral';
    }
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleString('th-TH');
  }
</script>

<svelte:head>
  <title>Monitoring Dashboard - Admin</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="container mx-auto px-4 py-8">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
        Monitoring Dashboard
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        ระบบติดตามและตรวจสอบ Token Activities และ Security Events
      </p>
    </div>

    {#if loading && !stats}
      <!-- <LoadingScreen /> -->
    {:else if error}
      <Alert color="error" title="เกิดข้อผิดพลาด">
        {error}
        <Button size="sm" color="primary" onclick={() => loadStats()} class="mt-2">
          ลองใหม่
        </Button>
      </Alert>
    {:else if stats}
      <!-- Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <div class="text-center">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Token Activities</h3>
            <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {stats.monitoring.totalTokenActivities}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">กิจกรรมทั้งหมด</p>
          </div>
        </Card>

        <Card>
          <div class="text-center">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Security Events</h3>
            <p class="text-3xl font-bold text-red-600 dark:text-red-400">
              {stats.monitoring.totalSecurityEvents}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">เหตุการณ์ความปลอดภัย</p>
          </div>
        </Card>

        <Card>
          <div class="text-center">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Active Tokens</h3>
            <p class="text-3xl font-bold text-green-600 dark:text-green-400">
              {stats.tokenStore.activeTokens}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">Token ที่ใช้งาน</p>
          </div>
        </Card>

        <Card>
          <div class="text-center">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Rate Limits</h3>
            <p class="text-3xl font-bold text-orange-600 dark:text-orange-400">
              {stats.rateLimit.blockedRecords}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">ถูกบล็อก</p>
          </div>
        </Card>
      </div>

      <!-- Recent Security Events -->
      <Card classBody="mb-8">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Security Events ล่าสุด
          </h2>
          <Button size="sm" color="primary" onclick={() => loadStats()}>
            รีเฟรช
          </Button>
        </div>

        {#if stats.monitoring.recentSecurityEvents.length > 0}
          <div class="space-y-4">
            {#each stats.monitoring.recentSecurityEvents as event}
              <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <div class="flex items-center gap-2 mb-2">
                      <Badge color={getSeverityColor(event.severity)}>
                        {event.severity.toUpperCase()}
                      </Badge>
                      <span class="text-sm font-medium text-gray-900 dark:text-white">
                        {event.type}
                      </span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-2">
                      {event.description}
                    </p>
                    <div class="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                      <span>IP: {event.ipAddress}</span>
                      <span>User: {event.userId || 'Unknown'}</span>
                      <span>Time: {formatDate(event.timestamp)}</span>
                    </div>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        {:else}
          <div class="text-center py-8 text-gray-500 dark:text-gray-400">
            ไม่มี Security Events ล่าสุด
          </div>
        {/if}
      </Card>

      <!-- Token Store Info -->
      <Card classBody="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          Token Store Statistics
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {stats.tokenStore.totalTokens}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">Total Tokens</p>
          </div>
          <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">
              {stats.tokenStore.activeTokens}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">Active Tokens</p>
          </div>
          <div class="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <p class="text-2xl font-bold text-red-600 dark:text-red-400">
              {stats.tokenStore.revokedTokens}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">Revoked Tokens</p>
          </div>
        </div>

        {#if stats.tokenStore.recentTokens.length > 0}
          <Table classBody="mb-8">
            <thead>
              <tr>
                <th>Token ID</th>
                <th>User ID</th>
                <th>Created</th>
                <th>Device</th>
              </tr>
            </thead>
            <tbody>
              {#each stats.tokenStore.recentTokens as token}
                <tr>
                  <td class="font-mono text-xs">{token.tokenId.substring(0, 8)}...</td>
                  <td class="font-mono text-xs">{token.userId.substring(0, 8)}...</td>
                  <td class="text-sm">{formatDate(token.createdAt)}</td>
                  <td class="font-mono text-xs">{token.deviceFingerprint}</td>
                </tr>
              {/each}
            </tbody>
          </Table>
        {/if}
      </Card>

      <!-- Rate Limit Info -->
      <Card>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          Rate Limit Statistics
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <p class="text-2xl font-bold text-gray-600 dark:text-gray-400">
              {stats.rateLimit.totalRecords}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">Total Records</p>
          </div>
          <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
              {stats.rateLimit.activeRecords}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">Active Records</p>
          </div>
          <div class="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <p class="text-2xl font-bold text-red-600 dark:text-red-400">
              {stats.rateLimit.blockedRecords}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">Blocked Records</p>
          </div>
        </div>
      </Card>
    {/if}
  </div>
</div> 