import type { PageServerLoad, Actions } from './$types';
import { redirect, fail } from '@sveltejs/kit';
import { authService } from '$lib/services/auth';
import { performSecurityChecks } from '$lib/utils/security-middleware';
import { revokeAllUserTokens } from '$lib/utils/token-rotation';
import { logger, LogCategory } from '$lib/utils/logger';
import { getClientIP, getUserAgent } from '$lib/utils/security';

export const load: PageServerLoad = async () => {
  // Redirect ไปหน้า dashboard หากเข้ามาโดยตรง
  throw redirect(302, '/dashboard');
};

export const actions: Actions = {
  default: async ({ request, cookies, locals }) => {
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    const sessionId = cookies.get('session_id');

    try {
      // ✅ Security checks (ไม่ต้องใช้ CSRF สำหรับ signout เพราะเป็น safe operation)
      const securityCheck = performSecurityChecks({ request, cookies, locals } as any, {
        requireCSRF: false, // Signout ไม่ต้องใช้ CSRF token
        checkSensitiveRateLimit: true,
        identifier: clientIP
      });

      if (!securityCheck.allowed) {
        logger.warn(LogCategory.AUTH, 'signout_security_failed', 'Signout security check failed', {
          error: securityCheck.error,
          clientIP,
          userAgent,
          sessionId
        });

        return fail(securityCheck.statusCode || 403, {
          error: securityCheck.error || 'Security validation failed'
        });
      }

      // ✅ Get tokens from cookies
      const authToken = cookies.get('auth_token');
      const refreshToken = cookies.get('refreshToken');
      const userId = locals.user?._id;

      logger.info(LogCategory.AUTH, 'signout_attempt', 'User attempting signout', {
        userId,
        hasAuthToken: !!authToken,
        hasRefreshToken: !!refreshToken,
        clientIP,
        userAgent,
        sessionId
      });

      // ✅ Call backend signout API to revoke tokens (ถ้ามี CSRF token)
      const csrfToken = cookies.get('csrf_token');

      console.log('Signout Debug:', {
        hasAuthToken: !!authToken,
        hasRefreshToken: !!refreshToken,
        hasCsrfToken: !!csrfToken,
        csrfTokenValue: csrfToken ? 'exists' : 'null'
      });

      if ((authToken || refreshToken) && csrfToken) {
        console.log('Calling backend API signout...');
        try {
          const result = await authService.signout(authToken, refreshToken);

          if (!result.success) {
            logger.warn(LogCategory.AUTH, 'signout_api_failed', 'Backend signout failed', {
              error: result.error,
              userId,
              clientIP,
              userAgent,
              sessionId
            });
          } else {
            logger.info(LogCategory.AUTH, 'signout_api_success', 'Backend signout successful', {
              userId,
              clientIP,
              userAgent,
              sessionId
            });
          }
        } catch (error) {
          logger.error(LogCategory.AUTH, 'signout_api_error', 'Signout API error', {
            error: error instanceof Error ? error.message : 'Unknown error',
            userId,
            clientIP,
            userAgent,
            sessionId
          });
        }
      } else {
        logger.info(LogCategory.AUTH, 'signout_skip_api', 'Skipping backend API call (no CSRF token)', {
          hasAuthToken: !!authToken,
          hasRefreshToken: !!refreshToken,
          hasCsrfToken: !!csrfToken,
          userId,
          clientIP,
          userAgent,
          sessionId
        });
      }

      // ✅ Revoke all refresh tokens for this user
      if (userId) {
        try {
          revokeAllUserTokens(userId);
          logger.info(LogCategory.AUTH, 'signout_tokens_revoked', 'All user tokens revoked', {
            userId,
            clientIP,
            userAgent,
            sessionId
          });
        } catch (error) {
          logger.error(LogCategory.AUTH, 'signout_token_revocation_failed', 'Token revocation failed', {
            error: error instanceof Error ? error.message : 'Unknown error',
            userId,
            clientIP,
            userAgent,
            sessionId
          });
        }
      }

      // ✅ Clear all auth-related cookies securely
      const cookiesToClear = [
        'auth_token',
        'refreshToken',
        'session_id',
        'csrf_token',
        'remember_me'
      ];

      console.log('Starting cookie deletion for:', cookiesToClear);

      // ลบ cookies ทีละตัวเพื่อ debug ได้ง่าย
      try {
        // ✅ ลบ cookies ด้วย options ที่ตรงกับที่ถูกสร้างไว้
        const cookieOptions = {
          path: '/',
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict' as const
        };

        const laxCookieOptions = {
          path: '/',
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax' as const
        };

        // ลบ cookies ทีละตัวด้วย options หลายแบบเพื่อให้แน่ใจ
        cookiesToClear.forEach(cookieName => {
          console.log(`Deleting ${cookieName}...`);
          
          // ลบด้วย strict sameSite (เหมือน signin และ hooks)
          cookies.delete(cookieName, cookieOptions);
          
          // ลบด้วย lax sameSite (เหมือน profile page)
          cookies.delete(cookieName, laxCookieOptions);
          
          // ลบด้วย path เฉพาะ
          cookies.delete(cookieName, { path: '/' });
          
          // ลบด้วย options ว่าง (fallback)
          cookies.delete(cookieName, { path: '/' });
          
          console.log(`✅ ${cookieName} deleted`);
        });

        console.log('✅ All cookies deleted successfully');
      } catch (error) {
        console.error('❌ Error during cookie deletion:', error);
      }

      logger.info(LogCategory.AUTH, 'signout_success', 'User signed out successfully', {
        userId,
        clientIP,
        userAgent,
        sessionId,
        cookiesCleared: cookiesToClear.length
      });

      // ✅ Redirect to signin page
      throw redirect(302, '/signin');

    } catch (error) {
      if (error instanceof Response) {
        throw error; // Re-throw redirects
      }

      logger.error(LogCategory.AUTH, 'signout_error', 'Signout process failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: locals.user?._id,
        clientIP,
        userAgent,
        sessionId
      });

      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการออกจากระบบ'
      });
    }
  }
};
