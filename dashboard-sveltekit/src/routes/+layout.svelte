<script lang="ts">
	import "../app.css";
	import { onMount } from "svelte";
	import { waitLocale } from "$lib/i18n";
	// import { authStore } from "$lib/stores/auth.svelte";

	interface Props {
		children: any;
		// data: { user?: any };
	}

	let { children }: Props = $props();
	let isLoading = $state(true);

	onMount(async () => {
		// รอให้ i18n โหลดเสร็จ
		await waitLocale();

		// ตั้งค่า user จาก SSR ถ้ามี
		// if (data?.user) {
		// 	authStore.setUserFromSSR(data.user);
		// }

		isLoading = false;
	});
</script>

{#if isLoading}
	<div class="min-h-screen flex items-center justify-center">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{:else}
	{@render children()}
{/if}
