import type { Handle } from '@sveltejs/kit';
import crypto from 'crypto';
import { apiUrl } from '$lib/config';
import { getClientIP, getUserAgent, generateSessionId } from '$lib/utils/security';
import { logger, measurePerformance, logSecurityEvent, LogCategory } from '$lib/utils/logger';
import { tokenCache } from '$lib/utils/cache';
import { validateSession, updateSessionActivity } from '$lib/utils/session';
import { applySecurityHeaders } from '$lib/utils/security-headers';
import { checkRateLimit } from '$lib/utils/rate-limit';
import {
  validateRefreshToken,
  rotateRefreshToken,
  createDeviceFingerprint,
  revokeRefreshToken
} from '$lib/utils/token-rotation';

export const handle: Handle = async ({ event, resolve }) => {
  const endTimer = measurePerformance('hooks_handle');
  const clientIP = getClientIP(event.request);
  const userAgent = getUserAgent(event.request);
  const sessionId = event.cookies.get('session_id') || generateSessionId();

  logger.setRequestId(sessionId);

  // ✅ Apply security headers (skip in development if needed)
  if (process.env.NODE_ENV !== 'development' || !process.env.DISABLE_CSP) {
    applySecurityHeaders(event);
  }

  // ✅ Check rate limiting for API endpoints
  if (event.url.pathname.startsWith('/api/')) {
    const rateLimitResult = checkRateLimit('API_GENERAL', clientIP);

    if (!rateLimitResult.allowed) {
      logger.warn(LogCategory.SYSTEM, 'rate_limit_exceeded', 'Rate limit exceeded', {
        path: event.url.pathname,
        clientIP,
        remaining: rateLimitResult.remaining,
        resetTime: rateLimitResult.resetTime,
        blocked: rateLimitResult.blocked
      }, { ip: clientIP, userAgent, sessionId });

      return new Response(JSON.stringify({
        success: false,
        error: 'Rate limit exceeded',
        resetTime: rateLimitResult.resetTime
      }), {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString()
        }
      });
    }
  }

  // ดึง token จาก cookie
  const authToken = event.cookies.get('auth_token');

  logger.debug(LogCategory.SYSTEM, 'hooks_auth_check', 'Checking authentication in hooks', {
    hasAuthToken: !!authToken,
    path: event.url.pathname,
    method: event.request.method,
    sessionId
  }, { ip: clientIP, userAgent, sessionId });

  if (authToken) {
    try {
      // ✅ Check if user has signed out (prevent auto-refresh after signout)
      const isSignoutPath = event.url.pathname === '/signout';
      const isSigninPath = event.url.pathname === '/signin';
      
      if (isSignoutPath) {
        logger.info(LogCategory.SYSTEM, 'hooks_signout_detected', 'Signout detected, skipping token refresh', {
          sessionId
        }, { ip: clientIP, userAgent, sessionId });
        
        endTimer();
        return resolve(event);
      }

      // Check cache first for performance - ใช้ hash ของ token แทน prefix
      const tokenHash = crypto.createHash('sha256').update(authToken).digest('hex').substring(0, 16);
      const cachedUser = tokenCache.getUser(tokenHash);
      if (cachedUser) {
        event.locals.user = cachedUser;
        event.locals.token = authToken;

        logger.debug(LogCategory.SYSTEM, 'hooks_cache_hit', 'User data retrieved from cache', {
          userId: cachedUser._id,
          sessionId
        }, { ip: clientIP, userAgent, sessionId });

        endTimer();
        return resolve(event);
      }

      logger.info(LogCategory.SYSTEM, 'hooks_token_validation', 'Validating token with backend', {
        sessionId
      }, { ip: clientIP, userAgent, sessionId });

      // ตรวจสอบ token กับ backend
      const validationTimer = measurePerformance('backend_token_validation');
      const fetchResponse = await fetch(`${apiUrl}/user/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'User-Agent': userAgent,
          'X-Forwarded-For': clientIP,
          'X-Real-IP': clientIP,
          'X-Session-ID': sessionId,
        }
      });

      if (!fetchResponse.ok) {
        throw new Error(`HTTP ${fetchResponse.status}: ${fetchResponse.statusText}`);
      }

      const response = await fetchResponse.json();
      validationTimer();

      logger.info(LogCategory.SYSTEM, 'hooks_validation_response', 'Backend validation response received', {
        success: true,
        sessionId
      }, { ip: clientIP, userAgent, sessionId });

      if (response.success && response.data) {
        // เพิ่ม user และ token ลงใน locals
        event.locals.user = response.data.user;
        event.locals.token = authToken;

        // Cache user data - ใช้ hash แทน prefix
        const tokenHash = crypto.createHash('sha256').update(authToken).digest('hex').substring(0, 16);
        tokenCache.cacheUser(tokenHash, response.data.user);

        logger.info(LogCategory.SYSTEM, 'hooks_auth_success', 'User authenticated successfully', {
          userId: response.data.user._id,
          email: `${response.data.user.email?.substring(0, 3)}***`,
          sessionId
        }, { ip: clientIP, userAgent, sessionId });
      }
    } catch (error) {
      // Token หมดอายุหรือไม่ถูกต้อง ลอง refresh token
      logger.info(LogCategory.SYSTEM, 'hooks_token_expired', 'Token expired or invalid, attempting refresh', {
        sessionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, { ip: clientIP, userAgent, sessionId });

      const refreshToken = event.cookies.get('refreshToken');

      if (refreshToken) {
        // ✅ Check if user has signed out (prevent auto-refresh after signout)
        const isSignoutPath = event.url.pathname === '/signout';
        const isSigninPath = event.url.pathname === '/signin';
        
        if (isSignoutPath) {
          logger.info(LogCategory.SYSTEM, 'hooks_signout_refresh_skipped', 'Signout detected, skipping token refresh', {
            sessionId
          }, { ip: clientIP, userAgent, sessionId });
          
          endTimer();
          return resolve(event);
        }

        try {
          // ✅ Validate refresh token with rotation system
          const deviceFingerprint = createDeviceFingerprint(userAgent, clientIP);
          const tokenValidation = validateRefreshToken(refreshToken, deviceFingerprint);

          if (!tokenValidation.valid) {
            logger.warn(LogCategory.SYSTEM, 'hooks_invalid_refresh_token', 'Invalid refresh token', {
              sessionId
            }, { ip: clientIP, userAgent, sessionId });

            // ไม่ลบ cookies อัตโนมัติ ให้ client จัดการเอง
            logger.info(LogCategory.SYSTEM, 'hooks_invalid_token_no_clear', 'Invalid refresh token, but not clearing cookies automatically', {
              sessionId
            }, { ip: clientIP, userAgent, sessionId });

            endTimer();
            return resolve(event);
          }

          // ✅ Check if token is revoked (additional security check)
          if (tokenValidation.record?.isRevoked) {
            logger.warn(LogCategory.SYSTEM, 'hooks_revoked_token', 'Refresh token is revoked', {
              sessionId,
              userId: tokenValidation.record.userId
            }, { ip: clientIP, userAgent, sessionId });

            // ไม่ลบ cookies อัตโนมัติ ให้ client จัดการเอง
            logger.info(LogCategory.SYSTEM, 'hooks_revoked_token_no_clear', 'Refresh token is revoked, but not clearing cookies automatically', {
              sessionId
            }, { ip: clientIP, userAgent, sessionId });

            endTimer();
            return resolve(event);
          }

          const refreshTimer = measurePerformance('backend_token_refresh');
          const refreshResponse = await fetch(`${apiUrl}/user/refresh-token`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': userAgent,
              'X-Forwarded-For': clientIP,
              'X-Real-IP': clientIP,
              'X-Session-ID': sessionId,
            },
            body: JSON.stringify({ refreshToken })
          });

          if (!refreshResponse.ok) {
            throw new Error(`HTTP ${refreshResponse.status}: ${refreshResponse.statusText}`);
          }

          const refreshData = await refreshResponse.json();
          refreshTimer();

          if (refreshData.success && refreshData.data) {
            // ✅ Rotate refresh token if needed
            let newRefreshToken = refreshData.data.refreshToken;

            if (tokenValidation.shouldRotate && tokenValidation.record) {
              const rotationResult = rotateRefreshToken(
                tokenValidation.record.tokenId,
                refreshData.data.user._id,
                deviceFingerprint
              );

              if (rotationResult) {
                newRefreshToken = rotationResult.token;
                logger.info(LogCategory.SYSTEM, 'hooks_token_rotated', 'Refresh token rotated', {
                  userId: refreshData.data.user._id,
                  oldTokenId: tokenValidation.record.tokenId,
                  newTokenId: rotationResult.tokenId,
                  sessionId
                }, { ip: clientIP, userAgent, sessionId });
              }
            }

            // อัปเดต cookies
            const cookieOptions = {
              path: '/',
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'strict' as const,
            };

            event.cookies.set('auth_token', refreshData.data.token, {
              ...cookieOptions,
              maxAge: 60 * 60 * 24 * 7, // 7 วัน
            });

            event.cookies.set('refreshToken', newRefreshToken, {
              ...cookieOptions,
              maxAge: 60 * 60 * 24 * 30, // 30 วัน
            });

            // ✅ Rotate session ID for security
            const newSessionId = crypto.randomUUID();
            event.cookies.set('session_id', newSessionId, {
              ...cookieOptions,
              maxAge: 60 * 60 * 24, // 1 วัน
            });

            // อัปเดต locals และ cache
            event.locals.user = refreshData.data.user;
            event.locals.token = refreshData.data.token;

            // Cache user data - ใช้ hash แทน prefix
            const newTokenHash = crypto.createHash('sha256').update(refreshData.data.token).digest('hex').substring(0, 16);
            tokenCache.cacheUser(newTokenHash, refreshData.data.user);
            tokenCache.cacheToken(refreshData.data.user._id, refreshData.data.token);

            logger.info(LogCategory.SYSTEM, 'hooks_token_refreshed', 'Token refreshed successfully', {
              userId: refreshData.data.user._id,
              sessionId: newSessionId,
              tokenRotated: tokenValidation.shouldRotate
            }, { ip: clientIP, userAgent, sessionId });
          }
        } catch (refreshError) {
          logger.error(LogCategory.SYSTEM, 'hooks_refresh_error', 'Error during token refresh', {
            error: refreshError instanceof Error ? refreshError.message : 'Unknown error',
            sessionId
          }, { ip: clientIP, userAgent, sessionId });

          logSecurityEvent(
            'token_refresh_exception',
            'Exception during token refresh in hooks',
            'high',
            { error: refreshError instanceof Error ? refreshError.message : 'Unknown error' },
            { ip: clientIP, userAgent, sessionId }
          );

          // ไม่ลบ cookies อัตโนมัติ ให้ client จัดการเอง
          logger.info(LogCategory.SYSTEM, 'hooks_refresh_error_no_clear', 'Token refresh error, but not clearing cookies automatically', {
            sessionId
          }, { ip: clientIP, userAgent, sessionId });
        }
      } else {
        logger.warn(LogCategory.SYSTEM, 'hooks_no_refresh_token', 'No refresh token available, clearing auth cookie', {
          sessionId
        }, { ip: clientIP, userAgent, sessionId });

        logSecurityEvent(
          'no_refresh_token',
          'No refresh token available in hooks',
          'medium',
          {},
          { ip: clientIP, userAgent, sessionId }
        );

        // ไม่ลบ cookies อัตโนมัติ ให้ client จัดการเอง
        logger.info(LogCategory.SYSTEM, 'hooks_no_refresh_token_no_clear', 'No refresh token available, but not clearing cookies automatically', {
          sessionId
        }, { ip: clientIP, userAgent, sessionId });
      }
    }
  } else {
    logger.debug(LogCategory.SYSTEM, 'hooks_no_token', 'No auth token found', {
      path: event.url.pathname,
      sessionId
    }, { ip: clientIP, userAgent, sessionId });
  }

  endTimer();
  return resolve(event);
};
